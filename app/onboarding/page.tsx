import { createClient, createServiceRoleClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ConversationalInterface } from '@/components/conversational-interface'

export default async function OnboardingPage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  // Check if user already has completed onboarding
  const { data: profile, error: profileError } = await supabase
    .from('partner_profiles')
    .select('onboarding_completed')
    .eq('id', user.id)
    .single()

  console.log('Onboarding - Profile check:', {
    userId: user.id,
    profile: profile ? { onboarding_completed: profile.onboarding_completed } : null,
    error: profileError
  });

  // If no profile exists, create one
  if (!profile && profileError) {
    console.log('Onboarding - Creating missing profile for user:', user.id);
    try {
      const serviceSupabase = await createServiceRoleClient();
      const { error: createError } = await serviceSupabase
        .from('partner_profiles')
        .insert({
          id: user.id,
          email: user.email || '',
          first_name: 'User',
          onboarding_completed: false
        });

      if (createError) {
        console.error('Error creating profile in onboarding:', createError);
      } else {
        console.log('Successfully created profile for user:', user.id);
      }
    } catch (error) {
      console.error('Exception creating profile:', error);
    }
  }

  // If onboarding already completed, redirect to dashboard
  if (profile?.onboarding_completed) {
    console.log('Onboarding - User already completed, redirecting to dashboard');
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h1 className="text-xl font-semibold text-gray-900">Getting Started</h1>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to AI Couple Mediation Assistant
          </h2>
          <p className="text-gray-600">
            Let&apos;s have a conversation to get to know you and understand how we can help improve your communication
          </p>
        </div>

        <div className="h-[600px]">
          <ConversationalInterface
            workflowId="onboarding"
          />
        </div>
      </div>
    </div>
  )
}
