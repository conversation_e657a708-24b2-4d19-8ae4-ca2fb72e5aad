'use client'

import { useActionState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { submitOnboardingStep } from './actions'
import { User } from '@supabase/supabase-js'

interface OnboardingFormProps {
  user: User
  existingProfile?: {
    first_name?: string
    last_name?: string
    communication_goals?: string[]
    main_frustrations?: string[]
    emotional_needs?: string[]
    preferred_discussion_frequency?: string
    onboarding_step?: number
  }
}

const COMMUNICATION_GOALS = [
  'Better listening',
  'Less arguing',
  'More empathy',
  'Clearer expression',
  'Conflict resolution',
  'Understanding needs'
]

const COMMON_FRUSTRATIONS = [
  'Interrupting',
  'Not feeling heard',
  'Defensive responses',
  'Avoiding difficult topics',
  'Passive-aggressive behavior',
  'Bringing up past issues'
]

const EMOTIONAL_NEEDS = [
  'Validation',
  'Understanding',
  'Respect',
  'Appreciation',
  'Security',
  'Connection'
]

export default function OnboardingForm({ user, existingProfile }: OnboardingFormProps) {
  const currentStep = existingProfile?.onboarding_step || 1
  const [state, formAction, isPending] = useActionState(submitOnboardingStep, { error: '' })

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {state.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700 text-sm">{state.error}</p>
        </div>
      )}

      {/* Progress indicator */}
      <div className="flex justify-between items-center mb-8">
        {[1, 2, 3, 4].map((num) => (
          <div
            key={num}
            className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              num <= currentStep
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-600'
            }`}
          >
            {num}
          </div>
        ))}
      </div>

      {/* Step 1: Basic Information */}
      {currentStep === 1 && (
        <form action={formAction} className="space-y-4">
          <input type="hidden" name="userId" value={user.id} />
          <input type="hidden" name="email" value={user.email} />
          <input type="hidden" name="step" value="1" />

          <h2 className="text-xl font-semibold mb-4">Basic Information</h2>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                name="firstName"
                defaultValue={existingProfile?.first_name || ''}
                required
                disabled={isPending}
              />
            </div>
            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                name="lastName"
                defaultValue={existingProfile?.last_name || ''}
                disabled={isPending}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="frequency">How often would you like to have structured discussions?</Label>
            <Select name="preferredFrequency" defaultValue={existingProfile?.preferred_discussion_frequency || ''} disabled={isPending}>
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="bi-weekly">Bi-weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end pt-6">
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Saving...' : 'Next'}
            </Button>
          </div>
        </form>
      )}

      {/* Step 2: Communication Goals */}
      {currentStep === 2 && (
        <form action={formAction} className="space-y-4">
          <input type="hidden" name="userId" value={user.id} />
          <input type="hidden" name="step" value="2" />

          <h2 className="text-xl font-semibold mb-4">Communication Goals</h2>
          <p className="text-gray-600 mb-4">What would you like to improve in your communication?</p>

          <div className="grid grid-cols-2 gap-3">
            {COMMUNICATION_GOALS.map((goal) => (
              <div key={goal} className="flex items-center space-x-2">
                <Checkbox
                  id={goal}
                  name="communicationGoals"
                  value={goal}
                  defaultChecked={existingProfile?.communication_goals?.includes(goal)}
                  disabled={isPending}
                />
                <Label htmlFor={goal} className="text-sm">{goal}</Label>
              </div>
            ))}
          </div>

          <div className="flex justify-between pt-6">
            <Button type="button" variant="outline" onClick={() => window.location.href = '/onboarding?step=1'} disabled={isPending}>
              Previous
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Saving...' : 'Next'}
            </Button>
          </div>
        </form>
      )}

      {/* Step 3: Frustrations */}
      {currentStep === 3 && (
        <form action={formAction} className="space-y-4">
          <input type="hidden" name="userId" value={user.id} />
          <input type="hidden" name="step" value="3" />

          <h2 className="text-xl font-semibold mb-4">Current Frustrations</h2>
          <p className="text-gray-600 mb-4">What communication patterns frustrate you most?</p>

          <div className="grid grid-cols-2 gap-3">
            {COMMON_FRUSTRATIONS.map((frustration) => (
              <div key={frustration} className="flex items-center space-x-2">
                <Checkbox
                  id={frustration}
                  name="mainFrustrations"
                  value={frustration}
                  defaultChecked={existingProfile?.main_frustrations?.includes(frustration)}
                  disabled={isPending}
                />
                <Label htmlFor={frustration} className="text-sm">{frustration}</Label>
              </div>
            ))}
          </div>

          <div className="flex justify-between pt-6">
            <Button type="button" variant="outline" onClick={() => window.location.href = '/onboarding?step=2'} disabled={isPending}>
              Previous
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Saving...' : 'Next'}
            </Button>
          </div>
        </form>
      )}

      {/* Step 4: Emotional Needs */}
      {currentStep === 4 && (
        <form action={formAction} className="space-y-4">
          <input type="hidden" name="userId" value={user.id} />
          <input type="hidden" name="step" value="4" />

          <h2 className="text-xl font-semibold mb-4">Emotional Needs</h2>
          <p className="text-gray-600 mb-4">What do you need most from your partner?</p>

          <div className="grid grid-cols-2 gap-3">
            {EMOTIONAL_NEEDS.map((need) => (
              <div key={need} className="flex items-center space-x-2">
                <Checkbox
                  id={need}
                  name="emotionalNeeds"
                  value={need}
                  defaultChecked={existingProfile?.emotional_needs?.includes(need)}
                  disabled={isPending}
                />
                <Label htmlFor={need} className="text-sm">{need}</Label>
              </div>
            ))}
          </div>

          <div>
            <Label htmlFor="partnerEmail">Partner&apos;s Email (Optional)</Label>
            <Input
              id="partnerEmail"
              name="partnerEmail"
              type="email"
              placeholder="We&apos;ll invite them to join"
              disabled={isPending}
            />
          </div>

          <div className="flex justify-between pt-6">
            <Button type="button" variant="outline" onClick={() => window.location.href = '/onboarding?step=3'} disabled={isPending}>
              Previous
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Completing Setup...' : 'Complete Setup'}
            </Button>
          </div>
        </form>
      )}
    </div>
  )
}
