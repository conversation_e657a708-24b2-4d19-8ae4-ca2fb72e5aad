'use server'

import { createClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'

export async function submitOnboardingStep(
  prevState: { error: string; success?: boolean },
  formData: FormData
) {
  const supabase = await createClient()

  const userId = formData.get('userId') as string
  const email = formData.get('email') as string
  const step = parseInt(formData.get('step') as string)

  // Validation
  if (!userId || !email) {
    return { error: 'User ID and email are required' }
  }

  if (!step || step < 1 || step > 4) {
    return { error: 'Invalid step number' }
  }

  let shouldRedirect = false
  let redirectPath = ''
  let error = ''

  try {
    // Get existing profile or create new one
    const { data: profile } = await supabase
      .from('partner_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    const updateData: Record<string, unknown> = {
      onboarding_step: step + 1,
      onboarding_completed: step === 4
    }

    // Process form data based on step
    if (step === 1) {
      // Basic Information
      const firstName = formData.get('firstName') as string
      const lastName = formData.get('lastName') as string
      const frequency = formData.get('preferredFrequency') as string

      if (!firstName) {
        return { error: 'First name is required' }
      }

      if (!frequency) {
        return { error: 'Discussion frequency is required' }
      }

      updateData.first_name = firstName
      updateData.last_name = lastName
      updateData.preferred_discussion_frequency = frequency
    } else if (step === 2) {
      // Communication Goals
      const goals = formData.getAll('communicationGoals') as string[]
      if (goals.length === 0) {
        return { error: 'Please select at least one communication goal' }
      }
      updateData.communication_goals = goals
    } else if (step === 3) {
      // Main Frustrations
      const frustrations = formData.getAll('mainFrustrations') as string[]
      if (frustrations.length === 0) {
        return { error: 'Please select at least one frustration' }
      }
      updateData.main_frustrations = frustrations
    } else if (step === 4) {
      // Emotional Needs
      const needs = formData.getAll('emotionalNeeds') as string[]
      if (needs.length === 0) {
        return { error: 'Please select at least one emotional need' }
      }
      updateData.emotional_needs = needs

      const partnerEmail = formData.get('partnerEmail') as string
      if (partnerEmail) {
        // TODO: Implement partner invitation logic
        console.log('Partner invitation will be sent to:', partnerEmail)
      }
    }

    // Database operation
    if (profile) {
      // Update existing profile
      const { error: updateError } = await supabase
        .from('partner_profiles')
        .update(updateData)
        .eq('id', userId)

      if (updateError) {
        console.error('Error updating profile:', updateError)
        error = 'Failed to update profile. Please try again.'
      } else {
        shouldRedirect = true
        redirectPath = step === 4 ? '/dashboard' : `/onboarding?step=${step + 1}`
      }
    } else {
      // Create new profile
      const { error: insertError } = await supabase
        .from('partner_profiles')
        .insert({
          id: userId,
          email: email,
          ...updateData
        })

      if (insertError) {
        console.error('Error creating profile:', insertError)
        error = 'Failed to create profile. Please try again.'
      } else {
        shouldRedirect = true
        redirectPath = step === 4 ? '/dashboard' : `/onboarding?step=${step + 1}`
      }
    }
  } catch (e) {
    console.error('Server action error:', e)
    error = e instanceof Error ? e.message : 'An unexpected error occurred'
  }

  // Handle redirects outside try-catch (Next.js 15 pattern)
  if (shouldRedirect) {
    revalidatePath('/onboarding')
    revalidatePath('/dashboard')
    redirect(redirectPath)
  }

  return { error, success: !error }
}

export async function getProfile(userId: string) {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('partner_profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error && error.code !== 'PGRST116') {
    console.error('Error fetching profile:', error)
    throw new Error('Failed to fetch profile')
  }

  return data
}
