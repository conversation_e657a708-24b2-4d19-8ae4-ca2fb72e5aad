'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient, createServiceRoleClient } from '@/lib/supabase/server'

function getBaseUrl() {
  // In production, use Vercel's system environment variables
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  // For local development
  if (process.env.NEXT_PUBLIC_SITE_URL) {
    return process.env.NEXT_PUBLIC_SITE_URL;
  }
  // Fallback for local development
  return 'http://localhost:3001';
}

export async function signUpAction(prevState: { error: string }, formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const repeatPassword = formData.get('repeatPassword') as string

  // Validation
  if (!email || !password || !repeatPassword) {
    return { error: 'All fields are required' }
  }

  if (password !== repeatPassword) {
    return { error: 'Passwords do not match' }
  }

  let shouldRedirect = false
  let redirectPath = ''
  let error = ''

  try {
    const supabase = await createClient()
    const { error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${getBaseUrl()}/auth/confirm?next=/onboarding`,
      },
    })

    if (signUpError) throw signUpError

    shouldRedirect = true
    redirectPath = '/auth/sign-up-success'
  } catch (e) {
    error = e instanceof Error ? e.message : 'An error occurred during sign up'
  }

  if (shouldRedirect) {
    redirect(redirectPath)
  }

  return { error }
}

export async function signInAction(prevState: { error: string }, formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  if (!email || !password) {
    return { error: 'Email and password are required' }
  }

  let shouldRedirect = false
  let redirectPath = ''
  let error = ''

  try {
    const supabase = await createClient()
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (signInError) throw signInError

    shouldRedirect = true
    redirectPath = '/dashboard'
  } catch (e) {
    error = e instanceof Error ? e.message : 'An error occurred during sign in'
  }

  if (shouldRedirect) {
    revalidatePath('/', 'layout')
    redirect(redirectPath)
  }

  return { error }
}

export async function forgotPasswordAction(prevState: { error: string }, formData: FormData) {
  const email = formData.get('email') as string

  if (!email) {
    return { error: 'Email is required' }
  }

  let shouldRedirect = false
  let redirectPath = ''
  let error = ''

  try {
    const supabase = await createClient()
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'https://ai-couple-mediation-assistant.vercel.app/auth/update-password',
    })

    if (resetError) throw resetError

    shouldRedirect = true
    redirectPath = '/auth/forgot-password-success'
  } catch (e) {
    error = e instanceof Error ? e.message : 'An error occurred sending reset email'
  }

  if (shouldRedirect) {
    redirect(redirectPath)
  }

  return { error }
}

export async function updatePasswordAction(prevState: { error: string }, formData: FormData) {
  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string
  const resetToken = formData.get('resetToken') as string
  const tokenType = formData.get('tokenType') as string

  if (!password || !confirmPassword) {
    return { error: 'Both password fields are required' }
  }

  if (password !== confirmPassword) {
    return { error: 'Passwords do not match' }
  }

  let shouldRedirect = false
  let redirectPath = ''
  let error = ''

  try {
    if (resetToken) {
      // Use service role client for password reset operations
      const supabaseServiceRole = await createServiceRoleClient()

      if (tokenType === 'recovery') {
        // Handle email OTP flow
        const { error: verifyError } = await supabaseServiceRole.auth.verifyOtp({
          type: 'recovery',
          token_hash: resetToken,
        })
        if (verifyError) throw verifyError
      } else {
        // Handle PKCE flow
        const { error: exchangeError } = await supabaseServiceRole.auth.exchangeCodeForSession(resetToken)
        if (exchangeError) throw exchangeError
      }

      // Update the password using service role
      const { error: updateError } = await supabaseServiceRole.auth.updateUser({
        password,
      })
      if (updateError) throw updateError
    } else {
      // Regular password update for authenticated users
      const supabase = await createClient()
      const { error: updateError } = await supabase.auth.updateUser({
        password,
      })
      if (updateError) throw updateError
    }

    shouldRedirect = true
    redirectPath = resetToken ? '/auth/login?message=Password updated successfully. Please log in with your new password.' : '/dashboard'
  } catch (e) {
    error = e instanceof Error ? e.message : 'An error occurred updating password'
  }

  if (shouldRedirect) {
    revalidatePath('/', 'layout')
    redirect(redirectPath)
  }

  return { error }
}

export async function signOutAction() {
  let shouldRedirect = false
  let redirectPath = ''

  try {
    const supabase = await createClient()
    const { error: signOutError } = await supabase.auth.signOut()

    if (signOutError) throw signOutError

    shouldRedirect = true
    redirectPath = '/'
  } catch {
    // For sign out, we'll redirect anyway even if there's an error
    shouldRedirect = true
    redirectPath = '/'
  }

  if (shouldRedirect) {
    revalidatePath('/', 'layout')
    redirect(redirectPath)
  }
}
