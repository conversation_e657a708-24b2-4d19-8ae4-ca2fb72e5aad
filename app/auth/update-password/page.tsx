import { UpdatePasswordForm } from "@/components/update-password-form";
import { redirect } from "next/navigation";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ code?: string; token_hash?: string; type?: string }>;
}) {
  const { code, token_hash, type } = await searchParams;

  // Check if we have a valid password reset token
  if (!code && !(token_hash && type === 'recovery')) {
    redirect("/auth/forgot-password?message=Invalid or missing reset token");
  }

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <UpdatePasswordForm resetToken={code || token_hash} tokenType={type} />
      </div>
    </div>
  );
}
