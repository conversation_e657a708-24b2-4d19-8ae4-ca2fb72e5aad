import { createClient, createServiceRoleClient } from "@/lib/supabase/server";
import { type EmailOtpType } from "@supabase/supabase-js";
import { redirect } from "next/navigation";
import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get("token_hash");
  const token = searchParams.get("token"); // PKCE token from email
  const type = searchParams.get("type") as EmailOtpType | null;
  const code = searchParams.get("code");
  const next = searchParams.get("next") ?? "/onboarding";

  // Log the parameters for debugging
  console.log("Confirmation parameters:", {
    token_hash: token_hash ? "present" : "missing",
    token: token ? "present" : "missing",
    type,
    code: code ? "present" : "missing",
    url: request.url,
    searchParams: Object.fromEntries(searchParams.entries())
  });

  const supabase = await createClient();

  // Helper function to ensure user has a profile
  async function ensureUserProfile(userId: string, userEmail: string) {
    try {
      const serviceSupabase = await createServiceRoleClient();

      // Check if profile exists
      const { data: existingProfile } = await serviceSupabase
        .from('partner_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (!existingProfile) {
        // Create profile for new user
        const { error: profileError } = await serviceSupabase
          .from('partner_profiles')
          .insert({
            id: userId,
            email: userEmail,
            first_name: 'User', // Default name, user can update later
            onboarding_completed: false
          });

        if (profileError) {
          console.error('Error creating user profile:', profileError);
        } else {
          console.log('Created profile for user:', userId);
        }
      }
    } catch (error) {
      console.error('Error ensuring user profile:', error);
    }
  }

  // Handle email OTP flow (token_hash + type)
  if (token_hash && type) {
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    });

    if (!error) {
      console.log("Email OTP confirmation successful, redirecting to:", next);

      // Ensure user has a profile
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await ensureUserProfile(user.id, user.email || '');
      }

      redirect(next);
    } else {
      console.error("Email OTP confirmation error:", error);
      redirect(`/auth/error?error=${encodeURIComponent(error?.message || 'Unknown error')}`);
    }
  }

  // Handle PKCE email confirmation (token + type from email link)
  if (token && (type === 'signup' || type === 'recovery')) {
    // For PKCE signup/recovery confirmation, we need to exchange the token
    try {
      const { error } = await supabase.auth.exchangeCodeForSession(token);

      if (!error) {
        console.log(`PKCE ${type} confirmation successful, redirecting to:`, next);

        // Ensure user has a profile
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          await ensureUserProfile(user.id, user.email || '');
        }

        // For password recovery, redirect to update password page
        const redirectPath = type === 'recovery' ? '/auth/update-password' : next;
        redirect(redirectPath);
      } else {
        console.error(`PKCE ${type} confirmation error:`, error);
        redirect(`/auth/error?error=${encodeURIComponent(error?.message || 'Unknown error')}`);
      }
    } catch (err) {
      console.error(`PKCE ${type} confirmation exception:`, err);
      redirect(`/auth/error?error=${encodeURIComponent(`Failed to confirm ${type}`)}`);
    }
  }

  // Handle PKCE flow (code)
  if (code) {
    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error) {
      console.log("PKCE code confirmation successful, redirecting to:", next);

      // Ensure user has a profile
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await ensureUserProfile(user.id, user.email || '');
      }

      redirect(next);
    } else {
      console.error("PKCE code confirmation error:", error);
      redirect(`/auth/error?error=${encodeURIComponent(error?.message || 'Unknown error')}`);
    }
  }

  // Log missing parameters
  console.error("Missing confirmation parameters:", {
    token_hash: !!token_hash,
    token: !!token,
    type: !!type,
    code: !!code,
    allParams: Object.fromEntries(searchParams.entries())
  });

  // redirect the user to an error page with some instructions
  redirect(`/auth/error?error=${encodeURIComponent('No valid confirmation parameters found in URL')}`);
}
