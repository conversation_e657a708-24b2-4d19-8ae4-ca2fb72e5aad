import { streamText, generateObject } from 'ai'
import { tool } from '@ai-sdk/provider-utils'
import { z } from 'zod'
import { workflowEngine } from '@/lib/workflows/workflow-engine'
import { createClient, createServiceRoleClient } from '@/lib/supabase/server'

/**
 * Sleep utility for retry delays
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Evaluates if a workflow should be completed based on the conversation history
 * Includes retry logic for rate limit handling
 */
async function evaluateWorkflowCompletion(userId: string, workflowId: string, retryCount = 0): Promise<{
  complete: boolean;
  summary?: string;
  keyInsights?: Record<string, unknown>;
  evaluationFailed?: boolean;
  error?: string;
}> {
  const context = workflowEngine.getContext(userId)
  if (!context) {
    return { complete: false }
  }

  const { definition, conversationHistory } = context

  // Ask the AI to evaluate if completion criteria are met
  const evaluationPrompt = `You are evaluating if a conversation has gathered SUFFICIENT information to complete the "${workflowId}" workflow.

COMPLETION CRITERIA (mark as met if there is reasonable information, not perfect detail):
${definition.completionCriteria.map((criteria, i) => `${i + 1}. ${criteria}`).join('\n')}

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n')}

EVALUATION GUIDELINES:
- Look for SUFFICIENT information, not perfect or exhaustive detail
- If the user has provided basic information about each criteria, consider it met
- Don't require deep psychological analysis - basic understanding is enough
- Focus on whether you have enough to help them move forward

If you have sufficient information for ALL criteria:
- Set "complete" to true
- Provide a comprehensive summary of the conversation
- Extract key insights as a JSON object with relevant fields (e.g., name, goals, challenges, etc.)

If you need more information for ANY criteria:
- Set "complete" to false
- Leave summary and keyInsights empty`

  try {
    const result = await generateObject({
      model: 'openai/gpt-4o-mini',
      schema: z.object({
        complete: z.boolean().describe('Whether all completion criteria are met'),
        summary: z.string().optional().describe('Summary of the conversation if complete'),
        keyInsights: z.record(z.string(), z.any()).optional().describe('Key insights extracted from the conversation'),
      }),
      prompt: evaluationPrompt,
      maxRetries: 0,
    })

    return result.object
  } catch (error) {
    console.error('Error evaluating workflow completion:', error)

    // Type-safe error handling
    const errorType = (error as { type?: string })?.type || 'unknown'
    const statusCode = (error as { statusCode?: number })?.statusCode
    const responseHeaders = (error as { responseHeaders?: Record<string, string> })?.responseHeaders

    console.log('Error type:', errorType, 'Status code:', statusCode)

    // Handle rate limit errors with persistent retry logic - NEVER GIVE UP
    if (statusCode === 429) {
      const retryAfter = responseHeaders?.['retry-after']
      // Use longer wait times: start at 5s, then 10s, 20s, 30s, then cap at 60s
      const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(Math.pow(2, retryCount) * 5000, 60000)

      console.log(`⏳ Rate limit hit, waiting ${waitTime/1000}s before retry (attempt ${retryCount + 1}) - NEVER GIVING UP`)
      await sleep(waitTime)

      // Recursive retry - NO LIMIT, keep trying until success
      return evaluateWorkflowCompletion(userId, workflowId, retryCount + 1)
    }

    return { complete: false, evaluationFailed: true, error: errorType }
  }
}

export async function POST(req: Request) {
  try {
    const { messages, workflowId } = await req.json()

    // Get authenticated user
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return new Response('Unauthorized', { status: 401 })
    }

    const userId = user.id

    // Handle workflow activation or commands
    const lastMessage = messages[messages.length - 1]
    const userMessage = lastMessage?.content?.toLowerCase().trim()

    // Handle special commands for multi-session workflows
    if (userMessage === 'new session' || userMessage === 'start new session') {
      const currentWorkflow = workflowEngine.getActiveWorkflow(userId)
      if (currentWorkflow && currentWorkflow.isComplete) {
        const config = workflowEngine.getWorkflowConfig(currentWorkflow.workflowId)
        if (config?.allowMultipleSessions) {
          // Start a fresh session of the same workflow type
          workflowEngine.startWorkflow(currentWorkflow.workflowId, userId)
          console.log(`🔄 Started fresh ${currentWorkflow.workflowId} session for user ${userId}`)
        }
      }
    } else if (userMessage === 'dashboard' || userMessage === 'go to dashboard' || userMessage === 'back to dashboard') {
      // Return a streaming response with redirect instruction
      const stream = new ReadableStream({
        start(controller) {
          const redirectData = JSON.stringify({
            type: 'redirect',
            redirect: '/dashboard',
            message: 'Redirecting to dashboard...'
          })
          controller.enqueue(new TextEncoder().encode(`data: ${redirectData}\n\n`))
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()
        }
      })

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      })
    }

    // Handle workflow activation
    if (workflowId && !workflowEngine.getActiveWorkflow(userId)) {
      try {
        await workflowEngine.startWorkflow(workflowId, userId)
        console.log(`Started workflow ${workflowId} for user ${userId}`)
      } catch (error) {
        console.error('Error starting workflow:', error)
        return new Response(error instanceof Error ? error.message : 'Workflow error', { status: 400 })
      }
    }

    // Get active workflow context
    const activeWorkflow = workflowEngine.getActiveWorkflow(userId)

    if (!activeWorkflow) {
      return new Response('No active workflow', { status: 400 })
    }

    // Add user message to workflow context
    if (lastMessage && lastMessage.role === 'user') {
      await workflowEngine.addMessage(userId, 'user', lastMessage.content)
    }

    // Generate context-aware prompt
    const contextPrompt = workflowEngine.generateContextPrompt(userId)

    // Define workflow completion tool
    const completeWorkflowTool = tool({
      description: 'Call this when you have gathered all required information and met the completion criteria for the current workflow. This will save the conversation and mark the workflow as complete.',
      inputSchema: z.object({
        summary: z.string().describe('A comprehensive natural language summary of the key information gathered during the conversation'),
        keyInsights: z.string().describe('JSON string of structured data extracted from the conversation (e.g., user name, goals, challenges, preferences)'),
      }),
      execute: async (params) => {
        try {
          const { summary, keyInsights: keyInsightsStr } = params
          const keyInsights = JSON.parse(keyInsightsStr) as Record<string, unknown>

          // Mark workflow as complete (this now saves to database automatically)
          await workflowEngine.completeWorkflow(userId, {
            summary,
            keyInsights,
            completedAt: new Date().toISOString()
          })

          // For onboarding workflow, also update partner_profiles with gathered data
          if (activeWorkflow.workflowId === 'onboarding') {
            // Extract structured data from keyInsights
            const profileUpdates: {
              onboarding_completed: boolean
              communication_goals?: string[]
              main_frustrations?: string[]
              emotional_needs?: string[]
              preferred_discussion_frequency?: 'daily' | 'weekly' | 'bi-weekly' | 'monthly'
              first_name?: string
            } = { onboarding_completed: true }

            // Map workflow data to profile fields based on actual AI output structure
            if (keyInsights.goals && Array.isArray(keyInsights.goals)) {
              profileUpdates.communication_goals = keyInsights.goals
            }

            if (keyInsights.painPoints && Array.isArray(keyInsights.painPoints)) {
              profileUpdates.main_frustrations = keyInsights.painPoints
            }

            if (keyInsights.emotionalNeeds && Array.isArray(keyInsights.emotionalNeeds)) {
              profileUpdates.emotional_needs = keyInsights.emotionalNeeds
            }

            // Handle communication preferences - look for structured or evening patterns
            if (keyInsights.communicationPreferences && Array.isArray(keyInsights.communicationPreferences)) {
              const prefs = keyInsights.communicationPreferences.join(' ').toLowerCase()
              if (prefs.includes('daily') || prefs.includes('every day')) {
                profileUpdates.preferred_discussion_frequency = 'daily'
              } else if (prefs.includes('weekly') && !prefs.includes('bi-weekly')) {
                profileUpdates.preferred_discussion_frequency = 'weekly'
              } else if (prefs.includes('bi-weekly') || prefs.includes('biweekly')) {
                profileUpdates.preferred_discussion_frequency = 'bi-weekly'
              } else if (prefs.includes('monthly')) {
                profileUpdates.preferred_discussion_frequency = 'monthly'
              } else {
                // Default to daily for structured communication
                profileUpdates.preferred_discussion_frequency = 'daily'
              }
            }

            // Also update first name if available
            if (keyInsights.name && typeof keyInsights.name === 'string') {
              profileUpdates.first_name = keyInsights.name
            }

            console.log('Updating partner profile with onboarding data:', {
              userId,
              updates: profileUpdates,
              originalKeyInsights: keyInsights
            })

            const { error: profileError } = await supabase
              .from('partner_profiles')
              .update(profileUpdates)
              .eq('id', userId)

            if (profileError) {
              console.error('Error updating partner profile:', profileError)
            } else {
              console.log('Successfully updated partner profile with onboarding data')
            }
          }

          return {
            success: true,
            message: 'Workflow completed successfully! Redirecting to dashboard...',
            redirect: '/dashboard'
          }
        } catch (error) {
          console.error('Error completing workflow:', error)
          return { success: false, message: 'An error occurred' }
        }
      }
    })

    // Create the conversation with workflow context
    // AI SDK automatically uses AI Gateway when model string is in creator/model-name format
    let result
    try {
      result = streamText({
        model: 'openai/gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: contextPrompt
          },
          ...messages
        ],
        temperature: 0.7,
        tools: {
          completeWorkflow: completeWorkflowTool
        },
        onFinish: async ({ toolResults }) => {
          // Check if completeWorkflow tool was called
          const completionResult = toolResults.find(
            (result) => result.toolName === 'completeWorkflow'
          )

          if (completionResult) {
            console.log('Workflow completed:', completionResult.output)
          }
        }
      })
    } catch (error) {
      console.error('AI response generation failed:', error)

      // Type-safe error handling
      const errorType = (error as { type?: string })?.type
      const statusCode = (error as { statusCode?: number })?.statusCode

      // Return error response for rate limits
      if (errorType === 'rate_limit_exceeded' || statusCode === 429) {
        return new Response(
          JSON.stringify({
            error: 'AI service is temporarily busy. Please try again in a moment.',
            type: 'rate_limit_error',
            retryAfter: 60
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              'Retry-After': '60'
            }
          }
        )
      }

      // Return generic error for other issues
      return new Response(
        JSON.stringify({
          error: 'AI service temporarily unavailable. Please try again.',
          type: 'service_error'
        }),
        {
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Convert to OpenAI-compatible streaming format
    const stream = new ReadableStream({
      async start(controller) {
        let fullResponse = ''

        for await (const chunk of result.textStream) {
          fullResponse += chunk

          // Send chunk in OpenAI format
          const data = JSON.stringify({
            choices: [{
              delta: {
                content: chunk
              }
            }]
          })

          controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`))
        }

        // Store the full assistant response
        await workflowEngine.addMessage(userId, 'assistant', fullResponse)

        // Only evaluate completion after reasonable conversation length to avoid unnecessary API calls
        const context = workflowEngine.getContext(userId)
        const messageCount = context?.conversationHistory.length || 0
        console.log(`📊 Conversation has ${messageCount} messages`)

        // Check if workflow is already complete
        if (context?.isComplete) {
          console.log('✅ Workflow already completed, skipping evaluation')
          return
        }

        // Only start evaluating completion after 6+ messages (3+ exchanges)
        if (messageCount >= 6) {
          console.log('🔍 Starting AI workflow completion evaluation (NEVER GIVING UP)...')
          try {
            // Get workflowId from active context instead of request (which might be undefined)
            const activeWorkflowId = context?.workflowId
            if (!activeWorkflowId) {
              console.log('❌ No active workflow found, skipping evaluation')
              return
            }

            const shouldComplete = await evaluateWorkflowCompletion(userId, activeWorkflowId)

          console.log('✅ Evaluation completed successfully:', shouldComplete.complete)
          console.log('🔍 shouldComplete object:', JSON.stringify(shouldComplete, null, 2))

          // No fallback logic - AI evaluation MUST succeed

          if (shouldComplete.complete) {
            console.log('🎉 Workflow completion criteria met!')
            console.log('Summary:', shouldComplete.summary)
            console.log('Key Insights:', shouldComplete.keyInsights)
            console.log(`🔧 About to update database for workflow: ${activeWorkflowId}, user: ${userId}`)

            // Mark workflow as complete
            workflowEngine.completeWorkflow(userId, shouldComplete.keyInsights || {})

            // Save to database
            await supabase
              .from('conversations')
              .update({
                summary: shouldComplete.summary,
                gathered_information: shouldComplete.keyInsights || {},
                is_complete: true,
                completed_at: new Date().toISOString()
              })
              .eq('user_id', userId)
              .eq('workflow_id', activeWorkflowId)

            // If this is the onboarding workflow, mark onboarding as complete
            if (activeWorkflowId === 'onboarding') {
              console.log(`🔄 Updating onboarding_completed for user ${userId}...`)

              // Use service role client for admin operations
              const adminSupabase = await createServiceRoleClient()
              const { error: profileUpdateError } = await adminSupabase
                .from('partner_profiles')
                .update({ onboarding_completed: true })
                .eq('id', userId)

              if (profileUpdateError) {
                console.error('❌ Error updating partner profile:', profileUpdateError)
              } else {
                console.log('✅ Successfully updated onboarding_completed to true')
              }
            }

            // Send completion signal to client based on workflow configuration
            const workflowConfig = workflowEngine.getWorkflowConfig(activeWorkflowId)
            const redirect = workflowConfig?.redirectOnCompletion || null
            const allowNewSession = workflowConfig?.allowMultipleSessions || false

            let message = 'Workflow completed successfully!'
            if (redirect) {
              message += ' Redirecting to dashboard...'
            } else if (allowNewSession) {
              message += ' You can start a new session anytime or return to your dashboard.'
            }

            const completionData = JSON.stringify({
              type: 'workflow_complete',
              summary: shouldComplete.summary,
              redirect: redirect,
              message: message,
              allowNewSession: allowNewSession,
              workflowId: activeWorkflowId
            })
            controller.enqueue(new TextEncoder().encode(`data: ${completionData}\n\n`))
          } else {
            console.log('❌ Workflow completion criteria not yet met')
          }
        } catch (evaluationError) {
          console.log('⚠️ AI evaluation failed, but message-count fallback already handled completion', evaluationError)
        }
        } else {
          console.log('💬 Conversation still developing, waiting for more exchanges before evaluating completion')
        }

        // Send done signal
        controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
        controller.close()
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })
  } catch (error) {
    console.error('Chat API error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}

// Helper endpoint to complete workflow
export async function PUT(req: Request) {
  try {
    const { gatheredInformation } = await req.json()

    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return new Response('Unauthorized', { status: 401 })
    }

    const userId = user.id
    const activeWorkflow = workflowEngine.getActiveWorkflow(userId)
    
    if (!activeWorkflow) {
      return new Response('No active workflow', { status: 400 })
    }

    // Complete the workflow
    workflowEngine.completeWorkflow(userId, gatheredInformation)

    // Store the conversation summary in the database
    // For now, we'll store it in a flexible format
    const conversationSummary = {
      workflowId: activeWorkflow.workflowId,
      userId,
      conversationHistory: activeWorkflow.conversationHistory,
      gatheredInformation,
      completedAt: new Date().toISOString(),
      summary: gatheredInformation.summary || 'Workflow completed successfully'
    }

    // Store in Supabase (we'll need to create a conversations table)
    const { error } = await supabase
      .from('conversations')
      .insert(conversationSummary)

    if (error) {
      console.error('Error storing conversation:', error)
      // Don't fail the request if storage fails
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Workflow completion error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}
