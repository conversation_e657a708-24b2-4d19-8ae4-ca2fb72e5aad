---
type: always
description: Critical Next.js 15, React 19, and Supabase patterns that must be followed to avoid common architectural mistakes
---

# Next.js 15 + React 19 + Supabase Development Rules

## 🚨 CRITICAL PATTERNS - NEVER VIOLATE

### 1. Next.js 15 Redirect Pattern
**NEVER call redirect() or revalidatePath() inside try-catch blocks.**

❌ WRONG:
```typescript
try {
  const result = await someOperation();
  redirect('/success'); // FAILS with NEXT_REDIRECT error
} catch (err) {
  redirect('/error'); // FAILS with NEXT_REDIRECT error
}
```

✅ CORRECT - Always use flag-based pattern:
```typescript
let shouldRedirect = false;
let redirectPath = '';

try {
  const result = await someOperation();
  shouldRedirect = true;
  redirectPath = '/success';
} catch (err) {
  shouldRedirect = true;
  redirectPath = `/error?error=${encodeURIComponent(err.message)}`;
}

if (shouldRedirect) {
  revalidatePath('/', 'layout');
  redirect(redirectPath); // ✅ Outside try-catch
}
```

### 2. React 19 Server Actions Pattern
**NEVER use client-side state management for forms. Always use Server Actions with useActionState.**

❌ WRONG - Old React 18 pattern:
```typescript
const [formData, setFormData] = useState({});
const handleSubmit = async (e) => {
  e.preventDefault();
  const response = await fetch('/api/auth', { method: 'POST' });
};
return <form onSubmit={handleSubmit}>
```

✅ CORRECT - React 19 Server Actions:
```typescript
// Server Action
export async function authAction(prevState: { error: string }, formData: FormData) {
  const email = formData.get('email') as string;
  // server-side logic with flag-based redirects
  return { error: '' };
}

// Client component
"use client";
import { useActionState } from "react";

export function AuthForm() {
  const [state, formAction, isPending] = useActionState(authAction, { error: '' });
  return (
    <form action={formAction}>
      <input name="email" type="email" required />
      <button type="submit" disabled={isPending}>
        {isPending ? "Loading..." : "Submit"}
      </button>
    </form>
  );
}
```

### 3. Supabase Authentication Patterns

**Use service role key for admin operations, anon key for user operations.**

❌ WRONG - Using anon key for password resets:
```typescript
const supabase = createClient(); // Uses anon key
await supabase.auth.updateUser({ password }); // FAILS - No permission
```

✅ CORRECT - Use service role for admin operations:
```typescript
// For password resets, user management
const supabaseServiceRole = createServiceRoleClient(); // Uses service role key
await supabaseServiceRole.auth.updateUser({ password }); // ✅ Works

// For regular user operations
const supabase = createClient(); // Uses anon key
await supabase.auth.getUser(); // ✅ Works
```

### 4. Environment Variables
**Use Vercel system environment variables instead of custom logic.**

❌ WRONG:
```typescript
const baseUrl = process.env.NODE_ENV === 'production' 
  ? 'https://myapp.vercel.app' 
  : 'http://localhost:3000';
```

✅ CORRECT:
```typescript
function getBaseUrl() {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  return process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001';
}
```

### 5. Next.js 15 SearchParams
**SearchParams are now promises in Next.js 15.**

❌ WRONG:
```typescript
export default function Page({ searchParams }: { searchParams: { code?: string } }) {
  const { code } = searchParams; // FAILS in Next.js 15
}
```

✅ CORRECT:
```typescript
export default async function Page({ 
  searchParams 
}: { 
  searchParams: Promise<{ code?: string }> 
}) {
  const { code } = await searchParams; // ✅ Works in Next.js 15
}
```

## 🎯 ARCHITECTURE PRINCIPLES

1. **Server-First Approach**: Use Server Actions instead of API routes for form submissions
2. **Vertical Implementation**: Implement complete features end-to-end before moving to next
3. **Simplicity Over Complexity**: Use built-in Next.js/React features before adding libraries

## 🔧 REQUIRED ENVIRONMENT VARIABLES

- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` (for client operations)
- `SUPABASE_SERVICE_ROLE_KEY` (for admin operations)

## 📋 PRE-IMPLEMENTATION CHECKLIST

Before implementing new features:
- [ ] Plan Server Actions vs client components
- [ ] Identify if service role permissions needed
- [ ] Plan redirect patterns (use flags)
- [ ] Consider environment variable requirements
- [ ] Plan vertical implementation approach

## 🚀 SUCCESS CRITERIA

A feature is complete when:
- [ ] Works locally in development
- [ ] Builds successfully (`npm run build`)
- [ ] Works in production deployment
- [ ] All error cases handled properly
- [ ] Follows established patterns in this rule
