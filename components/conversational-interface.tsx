'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Send, Bot, User } from 'lucide-react'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
}

interface ConversationalInterfaceProps {
  workflowId: string
  className?: string
}

export function ConversationalInterface({
  workflowId,
  className = ''
}: ConversationalInterfaceProps) {
  const router = useRouter()
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isWorkflowStarted, setIsWorkflowStarted] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Start workflow on component mount
  useEffect(() => {
    if (!isWorkflowStarted) {
      setIsWorkflowStarted(true)
      // Send initial message to start the workflow - we'll handle this inline
      const startWorkflow = async () => {
        try {
          const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              messages: [],
              workflowId: workflowId,
            }),
          })

          if (!response.ok) {
            // Handle rate limit errors on workflow start
            if (response.status === 429) {
              await response.json().catch(() => ({})) // Parse error response but don't store
              setMessages([{
                id: 'rate-limit-error',
                role: 'assistant',
                content: 'I\'m experiencing high demand right now. Please refresh the page in a moment to start your conversation.'
              }])
              return
            }
            throw new Error('Failed to start workflow')
          }

          const reader = response.body?.getReader()
          if (!reader) {
            throw new Error('No response body')
          }

          let assistantMessage = ''
          const assistantMessageId = Date.now().toString()

          // Add empty assistant message that we'll update
          setMessages([{ id: assistantMessageId, role: 'assistant', content: '' }])

          // Read the stream
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = new TextDecoder().decode(value)
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6)
                if (data === '[DONE]') continue

                try {
                  const parsed = JSON.parse(data)

                  // Handle workflow completion
                  if (parsed.type === 'workflow_complete') {
                    console.log('🎉 Workflow completed! Redirecting to:', parsed.redirect)
                    // Add completion message
                    setMessages([
                      { id: assistantMessageId, role: 'assistant', content: assistantMessage },
                      {
                        id: `completion-${Date.now()}`,
                        role: 'assistant',
                        content: '🎉 Great! I have all the information I need. Redirecting you to your dashboard...'
                      }
                    ])
                    // Redirect after a short delay
                    setTimeout(() => {
                      router.push(parsed.redirect || '/dashboard')
                    }, 2000)
                    return
                  }

                  // Handle regular streaming content
                  if (parsed.choices?.[0]?.delta?.content) {
                    assistantMessage += parsed.choices[0].delta.content
                    setMessages([{ id: assistantMessageId, role: 'assistant', content: assistantMessage }])
                  }
                } catch {
                  // Ignore parsing errors for partial chunks
                }
              }
            }
          }
        } catch (err) {
          console.error('Workflow start error:', err)
          setMessages([{
            id: 'workflow-start-error',
            role: 'assistant',
            content: 'I apologize, but I\'m having trouble starting our conversation. Please refresh the page and try again.'
          }])
        }
      }

      startWorkflow()
    }
  }, [workflowId, isWorkflowStarted, router])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const sendMessage = useCallback(async (userMessage: string) => {
    if (!userMessage.trim() && userMessage !== 'Hello! I&apos;m ready to start our conversation.') return

    setIsLoading(true)
    setError(null)

    // Add user message to the conversation (only if it's not the initial message)
    const newMessages = userMessage === 'Hello! I&apos;m ready to start our conversation.'
      ? messages
      : [...messages, { id: Date.now().toString(), role: 'user' as const, content: userMessage }]

    if (userMessage !== 'Hello! I&apos;m ready to start our conversation.') {
      setMessages(newMessages)
      setInput('')
    }

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: newMessages,
          workflowId: workflowId,
        }),
      })

      if (!response.ok) {
        // Handle rate limit errors gracefully
        if (response.status === 429) {
          const errorData = await response.json().catch(() => ({}))
          const retryAfter = errorData.retryAfter || 60

          setMessages(prev => [...prev, {
            id: `rate-limit-${Date.now()}`,
            role: 'assistant',
            content: `I'm experiencing high demand right now. Please wait ${retryAfter} seconds and try again. Your conversation progress is saved.`
          }])
          return
        }

        // Handle other errors
        setMessages(prev => [...prev, {
          id: `error-${Date.now()}`,
          role: 'assistant',
          content: 'I apologize, but I\'m having technical difficulties. Please try sending your message again.'
        }])
        return
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let assistantMessage = ''
      const assistantMessageId = (Date.now() + 1).toString()

      // Add empty assistant message that we'll update
      setMessages(prev => [...prev, { id: assistantMessageId, role: 'assistant', content: '' }])

      // Read the stream
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed = JSON.parse(data)

              // Handle direct redirects (e.g., dashboard command)
              if (parsed.type === 'redirect') {
                console.log('🔄 Direct redirect to:', parsed.redirect)
                setMessages(prev => [...prev, {
                  id: `redirect-${Date.now()}`,
                  role: 'assistant',
                  content: parsed.message || 'Redirecting...'
                }])
                setTimeout(() => {
                  router.push(parsed.redirect)
                }, 1000)
                return
              }

              // Handle workflow completion
              if (parsed.type === 'workflow_complete') {
                console.log(`🎉 ${parsed.workflowId} workflow completed!`, parsed.allowNewSession ? 'Allowing new session' : 'Redirecting to:', parsed.redirect)

                // Add completion message
                const completionMessage = parsed.message || '🎉 Great! I have all the information I need.'
                setMessages(prev => [...prev, {
                  id: `completion-${Date.now()}`,
                  role: 'assistant',
                  content: completionMessage
                }])

                // Handle redirect or allow new session
                if (parsed.redirect) {
                  // Redirect after a short delay (for onboarding and other single-session workflows)
                  setTimeout(() => {
                    router.push(parsed.redirect)
                  }, 2000)
                } else if (parsed.allowNewSession) {
                  // For multi-session workflows (coaching, discussion, etc.)
                  setTimeout(() => {
                    const workflowName = parsed.workflowId === 'coaching' ? 'coaching session' : `${parsed.workflowId} session`
                    setMessages(prev => [...prev, {
                      id: `new-session-${Date.now()}`,
                      role: 'assistant',
                      content: `💭 Would you like to start a new ${workflowName}, or would you prefer to return to your dashboard?`
                    }])

                    // Add action buttons for better UX
                    setTimeout(() => {
                      setMessages(prev => [...prev, {
                        id: `actions-${Date.now()}`,
                        role: 'assistant',
                        content: `You can:
• Type "new session" to start fresh
• Type "dashboard" to return to your dashboard
• Or just start talking about what's on your mind`
                      }])
                    }, 500)
                  }, 1000)
                }
                return
              }

              // Handle regular streaming content
              if (parsed.choices?.[0]?.delta?.content) {
                assistantMessage += parsed.choices[0].delta.content
                setMessages(prev =>
                  prev.map(msg =>
                    msg.id === assistantMessageId
                      ? { ...msg, content: assistantMessage }
                      : msg
                  )
                )
              }
            } catch {
              // Ignore parsing errors for partial chunks
            }
          }
        }
      }
    } catch (err) {
      console.error('Message send error:', err)
      setMessages(prev => [...prev, {
        id: `send-error-${Date.now()}`,
        role: 'assistant',
        content: 'I apologize, but I\'m having technical difficulties. Please try sending your message again.'
      }])
    } finally {
      setIsLoading(false)
    }
  }, [messages, isWorkflowStarted, workflowId, router])

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim()) {
      sendMessage(input)
    }
  }

  return (
    <div className={`flex flex-col h-full max-w-4xl mx-auto ${className}`}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="border-b">
          <CardTitle className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-blue-600" />
            AI Communication Assistant
          </CardTitle>
          <p className="text-sm text-gray-600">
            Let&apos;s have a natural conversation to get to know you better
          </p>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0">
          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.length === 0 && !isLoading && (
              <div className="text-center text-gray-500 py-8">
                <Bot className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Starting conversation...</p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`flex gap-3 max-w-[80%] ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {message.role === 'user' ? (
                      <User className="w-4 h-4" />
                    ) : (
                      <Bot className="w-4 h-4" />
                    )}
                  </div>
                  <div
                    className={`rounded-lg px-4 py-2 ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <p className="whitespace-pre-wrap">{message.content}</p>
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="flex gap-3 max-w-[80%]">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 bg-gray-200 text-gray-600">
                    <Bot className="w-4 h-4" />
                  </div>
                  <div className="rounded-lg px-4 py-2 bg-gray-100">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="border-t p-4">
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-sm">
                  Something went wrong. Please try again.
                </p>
              </div>
            )}

            <form onSubmit={handleFormSubmit} className="flex gap-2">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Type your message..."
                disabled={isLoading}
                className="flex-1"
                autoFocus
              />
              <Button
                type="submit"
                disabled={isLoading || !input.trim()}
                size="icon"
              >
                <Send className="w-4 h-4" />
              </Button>
            </form>

            <p className="text-xs text-gray-500 mt-2 text-center">
              Take your time to share your thoughts. This is a safe space for open conversation.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
