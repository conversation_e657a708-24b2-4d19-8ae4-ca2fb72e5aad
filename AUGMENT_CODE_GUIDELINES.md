# Augment Code Technical Guidelines

## Next.js 15 + React 19 + Supabase Best Practices

This document captures critical technical learnings from implementing authentication systems with modern React/Next.js patterns.

## 🚨 Critical Patterns - NEVER VIOLATE

### 1. Next.js 15 Redirect Pattern
**❌ WRONG - Never do this:**
```typescript
try {
  const result = await someOperation();
  if (error) {
    redirect('/error'); // ❌ FAILS - NEXT_REDIRECT error
  }
} catch (err) {
  redirect('/error'); // ❌ FAILS - NEXT_REDIRECT error
}
```

**✅ CORRECT - Always use flag-based pattern:**
```typescript
let shouldRedirect = false;
let redirectPath = '';
let errorMessage = '';

try {
  const result = await someOperation();
  if (error) {
    errorMessage = error.message;
  } else {
    shouldRedirect = true;
    redirectPath = '/success';
  }
} catch (err) {
  errorMessage = err.message;
}

if (errorMessage) {
  shouldRedirect = true;
  redirectPath = `/error?error=${encodeURIComponent(errorMessage)}`;
}

if (shouldRedirect) {
  redirect(redirectPath); // ✅ Outside try-catch
}
```

### 2. React 19 Server Actions Pattern
**❌ WRONG - Old React 18 pattern:**
```typescript
// Client component with useState
const [formData, setFormData] = useState({});
const [loading, setLoading] = useState(false);

const handleSubmit = async (e) => {
  e.preventDefault();
  setLoading(true);
  const response = await fetch('/api/auth', {
    method: 'POST',
    body: JSON.stringify(formData)
  });
  setLoading(false);
};

return <form onSubmit={handleSubmit}>
```

**✅ CORRECT - React 19 Server Actions:**
```typescript
// Server Action
export async function authAction(prevState: { error: string }, formData: FormData) {
  const email = formData.get('email') as string;
  // ... server-side logic
  return { error: '' };
}

// Client component
"use client";
import { useActionState } from "react";

export function AuthForm() {
  const [state, formAction, isPending] = useActionState(authAction, { error: '' });
  
  return (
    <form action={formAction}>
      <input name="email" type="email" required />
      <button type="submit" disabled={isPending}>
        {isPending ? "Loading..." : "Submit"}
      </button>
      {state.error && <p>{state.error}</p>}
    </form>
  );
}
```

### 3. Supabase Authentication Patterns

#### Service Role vs Anon Key
**❌ WRONG - Using anon key for admin operations:**
```typescript
const supabase = createClient(); // Uses anon key
await supabase.auth.updateUser({ password }); // ❌ FAILS - No permission
```

**✅ CORRECT - Use service role for admin operations:**
```typescript
// For password resets, user management
const supabaseServiceRole = createServiceRoleClient(); // Uses service role key
await supabaseServiceRole.auth.updateUser({ password }); // ✅ Works

// For regular user operations
const supabase = createClient(); // Uses anon key
await supabase.auth.getUser(); // ✅ Works
```

#### Password Reset Flow
**❌ WRONG - Complex session management:**
```typescript
// Don't try to establish sessions for password reset
const { error } = await supabase.auth.exchangeCodeForSession(code);
// Then check if user is authenticated...
```

**✅ CORRECT - Token-based form access:**
```typescript
// 1. Email link with token → Show form (no session required)
// 2. Form submission → Server action validates token and updates password
// 3. Redirect to login → User authenticates with new password

export default function UpdatePasswordPage({ searchParams }) {
  const { code } = await searchParams;
  
  if (!code) {
    redirect("/auth/forgot-password?message=Invalid reset token");
  }
  
  return <UpdatePasswordForm resetToken={code} />;
}
```

### 4. Environment Variables & Deployment

#### Vercel System Variables
**❌ WRONG - Custom environment detection:**
```typescript
const baseUrl = process.env.NODE_ENV === 'production' 
  ? 'https://myapp.vercel.app' 
  : 'http://localhost:3000';
```

**✅ CORRECT - Use Vercel system variables:**
```typescript
function getBaseUrl() {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  return process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001';
}
```

#### Supabase Environment Variables
**Required variables:**
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` (for client operations)
- `SUPABASE_SERVICE_ROLE_KEY` (for admin operations)

### 5. Next.js 15 SearchParams Pattern
**❌ WRONG - Old pattern:**
```typescript
export default function Page({ searchParams }: { searchParams: { code?: string } }) {
  const { code } = searchParams; // ❌ FAILS in Next.js 15
}
```

**✅ CORRECT - Promise-based pattern:**
```typescript
export default async function Page({ 
  searchParams 
}: { 
  searchParams: Promise<{ code?: string }> 
}) {
  const { code } = await searchParams; // ✅ Works in Next.js 15
}
```

## 🎯 Architecture Principles

### 1. Server-First Approach
- Use Server Actions instead of API routes for form submissions
- Leverage Server Components for data fetching
- Minimize client-side JavaScript

### 2. Vertical Implementation
- Implement complete features end-to-end
- Avoid horizontal scaffolding
- Test each feature completely before moving to next

### 3. Simplicity Over Complexity
- Use built-in Next.js/React features before adding libraries
- Leverage Supabase's integrated features
- Avoid over-engineering for MVP

## 🔧 Common Debugging Patterns

### 1. Server Action Issues
- Check if action is properly marked with `'use server'`
- Verify action is in `app/` directory, not `lib/`
- Ensure proper flag-based redirects

### 2. Supabase Auth Issues
- Check environment variables are correctly named
- Verify service role key for admin operations
- Check Supabase dashboard configuration (Site URL, redirect URLs)

### 3. Deployment Issues
- Verify all environment variables in Vercel
- Check build logs for TypeScript errors
- Test locally with production build: `npm run build`

## 📋 Pre-Implementation Checklist

Before implementing new features:
- [ ] Review these guidelines
- [ ] Plan Server Actions vs client components
- [ ] Identify if service role permissions needed
- [ ] Plan redirect patterns (use flags)
- [ ] Consider environment variable requirements
- [ ] Plan vertical implementation approach

## 🚀 Success Metrics

A feature is complete when:
- [ ] Works locally in development
- [ ] Builds successfully (`npm run build`)
- [ ] Works in production deployment
- [ ] All error cases handled properly
- [ ] No console errors or warnings
- [ ] Follows established patterns in this guide
