-- Partner Profiles Migration - AI Couple Mediation Assistant
-- Creates partner_profiles table extending Supabase auth.users
-- Migration executed successfully on: [DATE]

-- Create the partner_profiles table
CREATE TABLE partner_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT,
  partner_id UUID REFERENCES partner_profiles(id),
  
  -- Onboarding Data (FR1)
  communication_goals TEXT[],
  main_frustrations TEXT[],
  emotional_needs TEXT[],
  preferred_discussion_frequency TEXT CHECK (preferred_discussion_frequency IN ('daily', 'weekly', 'bi-weekly', 'monthly')),
  
  -- Profile Settings
  timezone TEXT DEFAULT 'UTC',
  notification_preferences JSONB DEFAULT '{"email": true, "push": false}',
  
  -- Onboarding Status
  onboarding_completed BOOLEAN DEFAULT FALSE,
  onboarding_step INTEGER DEFAULT 1,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT different_partners CHECK (id != partner_id)
);

-- <PERSON>reate indexes for performance
CREATE INDEX idx_partner_profiles_partner_id ON partner_profiles(partner_id);
CREATE INDEX idx_partner_profiles_email ON partner_profiles(email);
CREATE INDEX idx_partner_profiles_onboarding ON partner_profiles(onboarding_completed);

-- Enable Row Level Security
ALTER TABLE partner_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (non-recursive to avoid infinite recursion)
-- Users can manage their own profile
CREATE POLICY "Users can manage their own profile" ON partner_profiles
  FOR ALL USING (auth.uid() = id);

-- Users can view their partner's profile (if they have a partner_id set)
CREATE POLICY "Users can view their partner's profile" ON partner_profiles
  FOR SELECT USING (auth.uid() = partner_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_partner_profiles_updated_at 
    BEFORE UPDATE ON partner_profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
