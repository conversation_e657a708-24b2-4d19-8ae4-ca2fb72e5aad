-- Auto-create partner profile when user signs up
-- This ensures every authenticated user has a profile record

-- Function to create partner profile automatically
CREATE OR REPLACE FUNCTION create_partner_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO partner_profiles (id, email, first_name, last_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', 'User'),
    NEW.raw_user_meta_data->>'last_name'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile when user signs up
CREATE OR REPLACE TRIGGER create_partner_profile_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_partner_profile();

-- Also create profiles for existing users who don't have one
INSERT INTO partner_profiles (id, email, first_name, last_name)
SELECT 
  u.id,
  u.email,
  COALESCE(u.raw_user_meta_data->>'first_name', 'User') as first_name,
  u.raw_user_meta_data->>'last_name' as last_name
FROM auth.users u
LEFT JOIN partner_profiles p ON u.id = p.id
WHERE p.id IS NULL;
