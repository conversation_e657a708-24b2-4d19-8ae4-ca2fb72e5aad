-- Conversations Table Migration - AI Couple Mediation Assistant
-- Creates conversations table for storing natural language workflow conversations
-- Migration for Natural Language Workflow Architecture

-- Create the conversations table
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  workflow_id TEXT NOT NULL,
  
  -- Conversation Data (Natural Language Storage)
  conversation_history JSONB NOT NULL DEFAULT '[]',
  gathered_information JSONB NOT NULL DEFAULT '{}',
  summary TEXT,
  
  -- Workflow Status
  is_complete BOOLEAN DEFAULT FALSE,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_workflow_id ON conversations(workflow_id);
CREATE INDEX idx_conversations_user_workflow ON conversations(user_id, workflow_id);
CREATE INDEX idx_conversations_complete ON conversations(is_complete);

-- Enable Row Level Security
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can manage their own conversations
CREATE POLICY "Users can manage their own conversations" ON conversations
  FOR ALL USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_conversations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON conversations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_conversations_updated_at();

-- Add comments for documentation
COMMENT ON TABLE conversations IS 'Stores natural language conversations from workflow interactions';
COMMENT ON COLUMN conversations.conversation_history IS 'Array of conversation messages with role, content, and timestamp';
COMMENT ON COLUMN conversations.gathered_information IS 'Structured information extracted from the conversation';
COMMENT ON COLUMN conversations.summary IS 'Natural language summary of the conversation and key insights';
COMMENT ON COLUMN conversations.workflow_id IS 'Identifier of the workflow that generated this conversation';
