# AI Couple Mediation Assistant - Database Schema

## Overview
This document defines the complete database schema for the AI Couple Mediation Assistant MVP, designed for Supabase PostgreSQL with Row Level Security.

## Core Entities

### 1. Partners (Users)
Extends Supabase auth.users with couple-specific profile data.

```sql
CREATE TABLE partner_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  first_name TEXT NOT NULL,
  partner_id UUID REFERENCES partner_profiles(id),
  
  -- Onboarding Data (FR1)
  communication_goals TEXT[],
  main_frustrations TEXT[],
  emotional_needs TEXT[],
  preferred_discussion_frequency TEXT CHECK (preferred_discussion_frequency IN ('daily', 'weekly', 'bi-weekly', 'monthly')),
  
  -- Profile Settings
  timezone TEXT DEFAULT 'UTC',
  notification_preferences JSONB DEFAULT '{"email": true, "push": false}',
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT different_partners CHECK (id != partner_id)
);
```

### 2. Discussions
Core discussion management and scheduling.

```sql
CREATE TABLE discussions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  couple_id UUID NOT NULL, -- References the couple (derived from partner_profiles)
  
  -- Discussion Planning (FR3, FR8)
  title TEXT NOT NULL,
  description TEXT,
  priority_level INTEGER CHECK (priority_level BETWEEN 1 AND 5) DEFAULT 3,
  topics TEXT[] NOT NULL,
  
  -- Scheduling (FR8, FR13, FR16)
  scheduled_at TIMESTAMPTZ,
  discussion_type TEXT CHECK (discussion_type IN ('scheduled', 'on-demand')) DEFAULT 'scheduled',
  
  -- Status Management (FR11, FR14)
  status TEXT CHECK (status IN ('planned', 'ready', 'in-progress', 'completed', 'cancelled')) DEFAULT 'planned',
  
  -- Discussion Flow (FR4)
  current_phase TEXT CHECK (current_phase IN ('preparation', 'discussion', 'action-planning', 'completed')),
  current_speaker UUID REFERENCES partner_profiles(id),
  
  -- Outcomes (FR5)
  summary TEXT,
  agreed_actions UUID[], -- Array of action IDs
  
  -- Metadata
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 3. Discussion Preparation
Private coaching and preparation data (FR2, FR14, FR15).

```sql
CREATE TABLE discussion_preparations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  discussion_id UUID NOT NULL REFERENCES discussions(id) ON DELETE CASCADE,
  partner_id UUID NOT NULL REFERENCES partner_profiles(id) ON DELETE CASCADE,
  
  -- Private Coaching Data (FR2)
  emotions_clarified TEXT[],
  needs_identified TEXT[],
  proposed_solutions TEXT[],
  
  -- Readiness Check (FR11, FR14)
  emotional_readiness_score INTEGER CHECK (emotional_readiness_score BETWEEN 1 AND 10),
  is_ready BOOLEAN DEFAULT FALSE,
  readiness_notes TEXT,
  
  -- Topic Validation (FR14, FR15)
  confirmed_topics TEXT[],
  additional_topics TEXT[],
  topic_priorities JSONB, -- {"topic": priority_score}
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(discussion_id, partner_id)
);
```

### 4. Actions & Responsibilities
Action tracking with deadlines and progress (FR5, FR7, FR9).

```sql
CREATE TABLE actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  discussion_id UUID NOT NULL REFERENCES discussions(id) ON DELETE CASCADE,
  
  -- Action Details (FR5)
  title TEXT NOT NULL,
  description TEXT,
  assigned_to UUID NOT NULL REFERENCES partner_profiles(id),
  
  -- Timeline Management (FR7)
  deadline DATE NOT NULL,
  estimated_duration_hours INTEGER,
  
  -- Progress Tracking (FR9)
  status TEXT CHECK (status IN ('pending', 'in-progress', 'completed', 'overdue')) DEFAULT 'pending',
  progress_percentage INTEGER CHECK (progress_percentage BETWEEN 0 AND 100) DEFAULT 0,
  completion_notes TEXT,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);
```

### 5. Discussion Memory
Minimal memory storage for continuity (FR6).

```sql
CREATE TABLE discussion_memory (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  couple_id UUID NOT NULL,
  
  -- Memory Categories
  memory_type TEXT CHECK (memory_type IN ('topic', 'emotion', 'need', 'pattern', 'success')) NOT NULL,
  
  -- Memory Content
  content TEXT NOT NULL,
  context JSONB, -- Additional structured data
  
  -- Relevance & Retrieval
  importance_score INTEGER CHECK (importance_score BETWEEN 1 AND 10) DEFAULT 5,
  tags TEXT[],
  
  -- References
  related_discussion_id UUID REFERENCES discussions(id),
  related_partner_id UUID REFERENCES partner_profiles(id),
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_accessed_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 6. Discussion Messages
Real-time discussion flow and AI guidance (FR4).

```sql
CREATE TABLE discussion_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  discussion_id UUID NOT NULL REFERENCES discussions(id) ON DELETE CASCADE,
  
  -- Message Details
  sender_type TEXT CHECK (sender_type IN ('partner', 'ai', 'system')) NOT NULL,
  sender_id UUID REFERENCES partner_profiles(id), -- NULL for AI/system messages
  
  -- Content
  message_text TEXT NOT NULL,
  message_type TEXT CHECK (message_type IN ('statement', 'question', 'guidance', 'summary', 'action')) DEFAULT 'statement',
  
  -- AI Guidance Context
  guidance_context JSONB, -- For AI coaching prompts and responses
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Indexes for Performance

```sql
-- Partner lookups
CREATE INDEX idx_partner_profiles_partner_id ON partner_profiles(partner_id);
CREATE INDEX idx_partner_profiles_email ON partner_profiles(email);

-- Discussion queries
CREATE INDEX idx_discussions_couple_id ON discussions(couple_id);
CREATE INDEX idx_discussions_scheduled_at ON discussions(scheduled_at);
CREATE INDEX idx_discussions_status ON discussions(status);

-- Action tracking
CREATE INDEX idx_actions_assigned_to ON actions(assigned_to);
CREATE INDEX idx_actions_deadline ON actions(deadline);
CREATE INDEX idx_actions_status ON actions(status);

-- Memory retrieval
CREATE INDEX idx_discussion_memory_couple_id ON discussion_memory(couple_id);
CREATE INDEX idx_discussion_memory_type ON discussion_memory(memory_type);
CREATE INDEX idx_discussion_memory_importance ON discussion_memory(importance_score);

-- Real-time messages
CREATE INDEX idx_discussion_messages_discussion_id ON discussion_messages(discussion_id);
CREATE INDEX idx_discussion_messages_created_at ON discussion_messages(created_at);
```

## Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE partner_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE discussions ENABLE ROW LEVEL SECURITY;
ALTER TABLE discussion_preparations ENABLE ROW LEVEL SECURITY;
ALTER TABLE actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE discussion_memory ENABLE ROW LEVEL SECURITY;
ALTER TABLE discussion_messages ENABLE ROW LEVEL SECURITY;

-- Partners can only access their own data and their partner's
CREATE POLICY "Partners can view their couple's data" ON partner_profiles
  FOR ALL USING (
    auth.uid() = id OR 
    auth.uid() = partner_id OR
    id IN (SELECT partner_id FROM partner_profiles WHERE id = auth.uid())
  );

-- Similar policies for other tables...
```

## Key Design Decisions

1. **Couple Relationship**: Partners reference each other via `partner_id`
2. **Discussion Ownership**: Discussions belong to the couple, not individual partners
3. **Privacy**: Preparation data is private per partner until discussion starts
4. **Real-time**: Message table supports live discussion updates
5. **Memory System**: Flexible JSONB storage for AI context and learning
6. **Action Tracking**: Deadline-based with progress monitoring
7. **Scalability**: UUID primary keys, proper indexing, RLS for security

## Migration Strategy

1. Create tables in dependency order
2. Add indexes after table creation
3. Enable RLS and create policies
4. Seed with test data for development

---

**Last Updated**: 2025-09-28
**Version**: 1.0
**Status**: Ready for Implementation
