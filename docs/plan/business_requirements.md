# Business Requirements Document (BRD)

**Project Title:** AI Couple Mediation Assistant – MVP

**Document Purpose:**
Define business requirements for an MVP to assist couples with dysfunctional communication through AI-guided mediation.

---

## 1️⃣ Business Objectives

* Enable couples to **resolve conflicts constructively**.
* Reduce emotional escalation, passive-aggressive behaviors, and miscommunication.
* Provide a **structured framework for discussions** with actionable outcomes.
* Facilitate **gradual improvement in communication skills**.

---

## 2️⃣ Scope

### In-Scope

1. Partner onboarding (profile, frustrations, emotional needs, communication goals, discussion frequency)
2. Private AI coaching to clarify emotions and needs
3. Pre-discussion planning (topic prioritization, proposed solutions, action deadlines)
4. Guided discussions (scheduled and on-the-spot)
5. Action tracking with deadlines and duration aligned to discussion frequency
6. Minimal memory storage of discussion topics, emotions, needs, actions
7. Automatic engagement for scheduled discussions (reminder, emotional check-in, topic validation)

### Out-of-Scope

* Advanced analytics, scoring, and complex behavioral pattern detection
* Episodic memory and timeline visualization
* Visual dashboards and reporting
* Multimodal emotion detection (voice, gestures, facial expression)
* Direct behavioral suggestions by AI

---

## 3️⃣ Functional Requirements

| ID   | Requirement                                                                                        | Priority |
| ---- | -------------------------------------------------------------------------------------------------- | -------- |
| FR1  | Collect partner onboarding information                                                             | High     |
| FR2  | Enable private AI coaching to clarify emotions and needs                                           | High     |
| FR3  | Generate structured discussion plan                                                                | High     |
| FR4  | Guide real-time discussion with turn-taking and focus                                              | High     |
| FR5  | Capture agreed actions and responsibilities                                                        | High     |
| FR6  | Store minimal memory of discussion for continuity                                                  | High     |
| FR7  | Include action **duration and deadlines** aligned with discussion frequency                        | High     |
| FR8  | AI schedules discussion frequency based on preferences                                             | High     |
| FR9  | Track progress of actions between discussions                                                      | High     |
| FR10 | Allow partners to initiate **on-the-spot discussions**                                             | High     |
| FR11 | Validate readiness of both partners before starting discussion                                     | High     |
| FR12 | Propose alternative timing or brief coaching if partners are unavailable or emotionally unprepared | High     |
| FR13 | Send **scheduled discussion reminders** automatically                                              | High     |
| FR14 | Check emotional readiness and confirm topics before discussion                                     | High     |
| FR15 | Adjust discussion plan based on partner input before session                                       | High     |
| FR16 | Re-engage both partners at scheduled time to **launch guided discussion**                          | High     |

---

## 4️⃣ Non-Functional Requirements

* **Usability:** Simple interface, intuitive workflow
* **Security:** Secure, private storage of data
* **Performance:** Real-time AI responses
* **Scalability:** MVP designed for two users, future SaaS expansion possible

---

## 5️⃣ Success Criteria

* Structured, actionable discussions consistently occur
* Partners express emotions and needs clearly
* Actions are **small, realistic, and deadline-based**, and tracked effectively
* Discussions can be initiated on-the-spot or scheduled reliably
* Iterative improvement observed over successive discussions

