# AI Couple Mediation Assistant - Technology Stack

## Overview
This document defines the complete technology stack for the AI Couple Mediation Assistant MVP, based on the Vercel Supabase template foundation.

## Frontend Framework
- **Next.js 15** with App Router
  - Server Components for optimal performance
  - Server Actions for simplified backend logic
  - TypeScript for type safety
  - React 19 for latest features

## Backend Architecture
- **Server Actions** (no separate API needed)
  - Simplified data mutations
  - Built-in form handling
  - Type-safe server-side logic
- **Vercel Edge Runtime** for optimal performance

## Database & Storage
- **Supabase PostgreSQL**
  - Managed PostgreSQL database
  - Real-time subscriptions
  - Row Level Security (RLS)
  - Built-in backup and scaling
- **Supabase Storage** for file uploads (if needed)

## Authentication & Security
- **Supabase Auth**
  - Cookie-based sessions
  - Email/password authentication
  - Social login options (future)
  - Row Level Security policies

## AI Integration
- **Vercel AI SDK**
  - Unified interface for multiple LLM providers
  - Streaming responses
  - Built-in React hooks
  - Server Action integration
- **OpenAI GPT-4** as primary LLM
- **Anthrop<PERSON> Claude** as backup option

## UI & Styling
- **Tailwind CSS** for styling
  - Utility-first CSS framework
  - Responsive design system
  - Dark mode support
- **shadcn/ui** components
  - Pre-built accessible components
  - Consistent design system
  - Customizable themes

## Real-time Features
- **Supabase Real-time**
  - Live discussion updates
  - Partner status changes
  - Action progress tracking
  - No additional WebSocket setup needed

## Development Tools
- **TypeScript** for type safety
- **ESLint** for code quality
- **Prettier** for code formatting
- **Vercel CLI** for deployment

## Deployment & Hosting
- **Vercel** for frontend hosting
  - Automatic deployments from Git
  - Edge network distribution
  - Built-in analytics
- **Supabase Cloud** for database hosting
  - Automatic backups
  - Global distribution
  - Monitoring and alerts

## Key Benefits of This Stack
1. **Minimal Complexity**: Server Actions eliminate API layer
2. **Real-time Ready**: Supabase handles live updates
3. **AI-First**: Vercel AI SDK provides best-in-class LLM integration
4. **Scalable**: Both Vercel and Supabase scale automatically
5. **Developer Experience**: TypeScript + modern tooling
6. **Cost Effective**: Generous free tiers for MVP

## Dependencies Overview
```json
{
  "dependencies": {
    "next": "^15.0.0",
    "react": "^19.0.0",
    "typescript": "^5.0.0",
    "@supabase/supabase-js": "^2.0.0",
    "@supabase/ssr": "^0.5.0",
    "ai": "^3.0.0",
    "tailwindcss": "^3.0.0",
    "@radix-ui/react-*": "^1.0.0"
  }
}
```

## Database Schema Strategy
- Use Supabase migrations for schema management
- Implement Row Level Security for data protection
- Design for real-time subscriptions
- Plan for horizontal scaling

## AI Conversation Architecture
- Structured prompts for different conversation types
- Context management for ongoing discussions
- Memory system for partner preferences
- Fallback strategies for AI failures

## Security Considerations
- Row Level Security for all data access
- Server-side validation for all inputs
- Secure API key management via environment variables
- HTTPS everywhere via Vercel

---

**Last Updated**: 2025-09-28
**Version**: 1.0
**Status**: Approved for MVP Development
