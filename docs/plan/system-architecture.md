# AI Couple Mediation Assistant - System Architecture

## Overview
This document defines the complete system architecture for the AI Couple Mediation Assistant MVP, built on Next.js 15 with Supabase and Vercel AI SDK.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Client (Browser)                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Partner A     │  │   Partner B     │  │  AI Coach    │ │
│  │   Interface     │  │   Interface     │  │  Interface   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 Next.js 15 App Router                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Server          │  │ Server          │  │ AI           │ │
│  │ Components      │  │ Actions         │  │ Integration  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    External Services                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Supabase      │  │  Vercel AI      │  │   OpenAI     │ │
│  │   Database      │  │   Gateway       │  │   API        │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Authentication & User Management
**Technology**: Supabase Auth with cookie-based sessions

```typescript
// app/auth/
├── login/
│   └── page.tsx              // Login form
├── signup/
│   └── page.tsx              // Registration form
├── callback/
│   └── route.ts              // OAuth callback handler
└── actions.ts                // Auth server actions
```

**Key Features:**
- Email/password authentication
- Partner linking during onboarding
- Session management across Server/Client Components
- Automatic redirect handling

### 2. Partner Profile System
**Purpose**: Onboarding and profile management (FR1)

```typescript
// app/profile/
├── onboarding/
│   ├── page.tsx              // Multi-step onboarding
│   ├── steps/
│   │   ├── basic-info.tsx
│   │   ├── communication-goals.tsx
│   │   ├── frustrations.tsx
│   │   └── partner-linking.tsx
│   └── actions.ts            // Profile creation actions
├── edit/
│   └── page.tsx              // Profile editing
└── actions.ts                // Profile management actions
```

**Data Flow:**
1. User completes onboarding steps
2. Server Actions validate and store profile data
3. Partner linking creates couple relationship
4. Redirect to dashboard upon completion

### 3. Discussion Management System
**Purpose**: Discussion planning, scheduling, and execution (FR3, FR4, FR8)

```typescript
// app/discussions/
├── page.tsx                  // Discussion list/dashboard
├── new/
│   └── page.tsx              // Create new discussion
├── [id]/
│   ├── page.tsx              // Discussion detail view
│   ├── prepare/
│   │   └── page.tsx          // Private preparation (FR2)
│   ├── live/
│   │   └── page.tsx          // Live discussion interface
│   └── summary/
│       └── page.tsx          // Discussion summary & actions
├── components/
│   ├── discussion-card.tsx
│   ├── preparation-form.tsx
│   ├── live-chat.tsx
│   └── action-planner.tsx
└── actions.ts                // Discussion CRUD operations
```

**State Management:**
- Discussion status: planned → ready → in-progress → completed
- Real-time updates via Supabase subscriptions
- Turn-taking management for structured conversations

### 4. AI Integration Layer
**Purpose**: AI coaching, discussion guidance, and conversation management

```typescript
// app/ai/
├── coaching/
│   ├── route.ts              // Private coaching API
│   └── prompts.ts            // Coaching prompt templates
├── discussion/
│   ├── route.ts              // Discussion guidance API
│   └── prompts.ts            // Discussion prompt templates
├── planning/
│   ├── route.ts              // Discussion planning API
│   └── prompts.ts            // Planning prompt templates
└── utils/
    ├── conversation-context.ts
    ├── memory-retrieval.ts
    └── prompt-builder.ts
```

**AI Conversation Flow:**
1. **Private Coaching** (FR2): Individual emotion/need clarification
2. **Discussion Planning** (FR3): Consolidate inputs into structured agenda
3. **Live Guidance** (FR4): Real-time conversation facilitation
4. **Action Extraction** (FR5): Generate actionable items from discussion

### 5. Real-time Communication
**Purpose**: Live discussions and status updates (FR4, FR11)

```typescript
// app/components/real-time/
├── discussion-room.tsx       // Main discussion interface
├── message-stream.tsx        // Real-time message display
├── turn-indicator.tsx        // Current speaker indicator
├── readiness-checker.tsx     // Partner readiness validation
└── ai-guidance.tsx           // AI coaching overlay
```

**Real-time Features:**
- Live message streaming via Supabase subscriptions
- Partner presence indicators
- Turn-taking enforcement
- AI guidance injection

### 6. Action Tracking System
**Purpose**: Action management and progress tracking (FR5, FR7, FR9)

```typescript
// app/actions/
├── page.tsx                  // Action dashboard
├── [id]/
│   └── page.tsx              // Action detail/edit
├── components/
│   ├── action-card.tsx
│   ├── progress-tracker.tsx
│   └── deadline-reminder.tsx
└── actions.ts                // Action CRUD operations
```

**Features:**
- Deadline-based action tracking
- Progress percentage updates
- Automatic status updates (pending → in-progress → completed/overdue)
- Integration with discussion outcomes

## Server Actions Design

### Authentication Actions
```typescript
// app/auth/actions.ts
export async function signUp(formData: FormData)
export async function signIn(formData: FormData)
export async function signOut()
export async function linkPartner(partnerEmail: string)
```

### Profile Actions
```typescript
// app/profile/actions.ts
export async function createProfile(profileData: ProfileData)
export async function updateProfile(id: string, updates: Partial<ProfileData>)
export async function getPartnerProfile(partnerId: string)
```

### Discussion Actions
```typescript
// app/discussions/actions.ts
export async function createDiscussion(discussionData: DiscussionData)
export async function updateDiscussionStatus(id: string, status: DiscussionStatus)
export async function submitPreparation(discussionId: string, preparation: PreparationData)
export async function startDiscussion(discussionId: string)
export async function completeDiscussion(id: string, summary: string, actions: ActionData[])
```

### AI Integration Actions
```typescript
// app/ai/actions.ts
export async function getCoachingResponse(input: string, context: CoachingContext)
export async function generateDiscussionPlan(preparations: PreparationData[])
export async function getDiscussionGuidance(discussionId: string, currentPhase: string)
export async function extractActions(discussionContent: string)
```

## Data Flow Patterns

### 1. Discussion Lifecycle
```
Onboarding → Profile Creation → Discussion Planning → Preparation → 
Readiness Check → Live Discussion → Action Planning → Progress Tracking
```

### 2. AI Conversation Context
```
User Input → Context Retrieval → Prompt Construction → 
LLM Request → Response Processing → UI Update → Memory Storage
```

### 3. Real-time Updates
```
User Action → Server Action → Database Update → 
Supabase Subscription → Client Update → UI Refresh
```

## Security Architecture

### Row Level Security (RLS)
- Partners can only access their couple's data
- Preparation data is private until discussion starts
- Actions are visible to both partners
- Memory data is couple-scoped

### API Security
- Server Actions provide built-in CSRF protection
- All database operations go through RLS policies
- AI API keys stored securely in environment variables
- No direct database access from client

## Performance Considerations

### Server Components
- Use Server Components for data fetching
- Minimize client-side JavaScript
- Leverage Next.js caching strategies

### Real-time Optimization
- Selective Supabase subscriptions
- Optimistic UI updates
- Message batching for high-frequency updates

### AI Response Optimization
- Streaming responses for better UX
- Context caching to reduce token usage
- Fallback strategies for AI failures

## Deployment Architecture

### Vercel Deployment
- Automatic deployments from Git
- Edge runtime for optimal performance
- Environment variable management
- Preview deployments for testing

### Supabase Integration
- Automatic database provisioning
- Real-time subscriptions
- Built-in authentication
- Backup and monitoring

---

**Last Updated**: 2025-09-28
**Version**: 1.0
**Status**: Ready for Implementation
