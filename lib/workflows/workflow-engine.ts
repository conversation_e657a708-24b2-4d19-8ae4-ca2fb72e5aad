import fs from 'fs'
import path from 'path'
import { createServiceRoleClient } from '@/lib/supabase/server'

export interface WorkflowDefinition {
  id: string
  goal: string
  requiredInformation: string[]
  conversationStyle: string
  conversationGuidelines: string[]
  sampleOpening: string
  sampleFollowupQuestions: string[]
  completionCriteria: string[]
  dataStorageFormat: string
  transition: string
  content: string // Full markdown content
}

export interface WorkflowSession {
  sessionId: string
  workflowId: string
  definition: WorkflowDefinition
  conversationHistory: ConversationMessage[]
  gatheredInformation: Record<string, unknown>
  isComplete: boolean
  startedAt: Date
  completedAt?: Date
}

export interface WorkflowContext {
  workflowId: string
  definition: WorkflowDefinition
  conversationHistory: ConversationMessage[]
  gatheredInformation: Record<string, unknown>
  isComplete: boolean
  startedAt: Date
  completedAt?: Date
}

export interface WorkflowConfig {
  allowMultipleSessions: boolean
  persistCompletion: boolean
  redirectOnCompletion?: string
}

export interface ConversationMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

class WorkflowEngine {
  private workflows: Map<string, WorkflowDefinition> = new Map()
  private activeContexts: Map<string, WorkflowContext> = new Map() // Keep for current session only

  // Workflow configurations
  private workflowConfigs: Map<string, WorkflowConfig> = new Map([
    ['onboarding', {
      allowMultipleSessions: false,
      persistCompletion: true,
      redirectOnCompletion: '/dashboard'
    }],
    ['coaching', {
      allowMultipleSessions: true,
      persistCompletion: false
    }],
    ['discussion', {
      allowMultipleSessions: true,
      persistCompletion: false
    }]
  ])

  constructor() {
    this.loadWorkflows()
  }

  private loadWorkflows() {
    const workflowsDir = path.join(process.cwd(), 'workflows')
    
    if (!fs.existsSync(workflowsDir)) {
      console.warn('Workflows directory not found')
      return
    }

    const files = fs.readdirSync(workflowsDir)
    
    for (const file of files) {
      if (file.endsWith('.md')) {
        try {
          const filePath = path.join(workflowsDir, file)
          const content = fs.readFileSync(filePath, 'utf-8')
          const workflow = this.parseWorkflowDefinition(content)
          this.workflows.set(workflow.id, workflow)
          console.log(`Loaded workflow: ${workflow.id}`)
        } catch (error) {
          console.error(`Error loading workflow ${file}:`, error)
        }
      }
    }
  }

  private parseWorkflowDefinition(content: string): WorkflowDefinition {
    const lines = content.split('\n')
    const workflow: Partial<WorkflowDefinition> = { content }

    let currentSection = ''
    let currentContent: string[] = []

    for (const line of lines) {
      if (line.startsWith('## ')) {
        // Process previous section
        if (currentSection && currentContent.length > 0) {
          this.processSection(workflow, currentSection, currentContent.join('\n').trim())
        }
        
        // Start new section
        currentSection = line.replace('## ', '').toLowerCase().replace(/\s+/g, '')
        currentContent = []
      } else if (line.startsWith('# ')) {
        // Skip main title
        continue
      } else {
        currentContent.push(line)
      }
    }

    // Process final section
    if (currentSection && currentContent.length > 0) {
      this.processSection(workflow, currentSection, currentContent.join('\n').trim())
    }

    // Validate required fields
    if (!workflow.id) {
      throw new Error('Workflow must have an ID')
    }

    return workflow as WorkflowDefinition
  }

  private processSection(workflow: Partial<WorkflowDefinition>, section: string, content: string) {
    switch (section) {
      case 'workflowid':
        workflow.id = content.replace(/`/g, '').trim()
        break
      case 'goal':
        workflow.goal = content
        break
      case 'requiredinformation':
        workflow.requiredInformation = this.parseListItems(content)
        break
      case 'conversationstyle':
        workflow.conversationStyle = content
        break
      case 'conversationguidelines':
        workflow.conversationGuidelines = this.parseNumberedList(content)
        break
      case 'sampleopening':
        workflow.sampleOpening = content.replace(/^"|"$/g, '').trim()
        break
      case 'samplefollow-upquestions':
      case 'samplefollowupquestions':
        workflow.sampleFollowupQuestions = this.parseListItems(content)
        break
      case 'completioncriteria':
        workflow.completionCriteria = this.parseNumberedList(content)
        break
      case 'datastorageformat':
        workflow.dataStorageFormat = content
        break
      case 'transition':
        workflow.transition = content
        break
    }
  }

  private parseListItems(content: string): string[] {
    return content
      .split('\n')
      .filter(line => line.trim().startsWith('-') || line.trim().startsWith('*'))
      .map(line => line.replace(/^[-*]\s*/, '').replace(/^\*\*([^*]+)\*\*:\s*/, '').trim())
      .filter(item => item.length > 0)
  }

  private parseNumberedList(content: string): string[] {
    return content
      .split('\n')
      .filter(line => /^\d+\./.test(line.trim()))
      .map(line => line.replace(/^\d+\.\s*/, '').replace(/^\*\*([^*]+)\*\*\s*-?\s*/, '').trim())
      .filter(item => item.length > 0)
  }

  public getWorkflow(id: string): WorkflowDefinition | undefined {
    return this.workflows.get(id)
  }

  public listWorkflows(): WorkflowDefinition[] {
    return Array.from(this.workflows.values())
  }

  public async startWorkflow(workflowId: string, userId: string): Promise<WorkflowContext> {
    const definition = this.getWorkflow(workflowId)
    if (!definition) {
      throw new Error(`Workflow not found: ${workflowId}`)
    }

    const config = this.workflowConfigs.get(workflowId) || {
      allowMultipleSessions: true,
      persistCompletion: false
    }

    // Check if there's an existing active workflow in memory
    const existingContext = this.activeContexts.get(userId)

    // Handle workflow-specific logic
    if (existingContext && existingContext.workflowId === workflowId && !existingContext.isComplete) {
      console.log(`📋 Continuing active ${workflowId} session for user ${userId}`)
      return existingContext
    }

    // Try to load from database
    const dbContext = await this.loadWorkflowFromDB(userId, workflowId)
    if (dbContext && !dbContext.isComplete) {
      console.log(`💾 Loaded ${workflowId} workflow from database for user ${userId}`)
      this.activeContexts.set(userId, dbContext)
      return dbContext
    }

    // For single-session workflows, check if already completed
    if (!config.allowMultipleSessions && dbContext?.isComplete) {
      console.log(`✅ ${workflowId} already completed for user ${userId}`)
      throw new Error(`${workflowId} workflow already completed`)
    }

    // Create new workflow context
    const context: WorkflowContext = {
      workflowId,
      definition,
      conversationHistory: [],
      gatheredInformation: {},
      isComplete: false,
      startedAt: new Date()
    }

    this.activeContexts.set(userId, context)
    console.log(`🚀 Started new ${workflowId} workflow for user ${userId}`)
    return context
  }

  public getActiveWorkflow(userId: string): WorkflowContext | undefined {
    return this.activeContexts.get(userId)
  }

  public async addMessage(userId: string, role: 'user' | 'assistant', content: string): Promise<void> {
    const context = this.activeContexts.get(userId)
    if (!context) {
      throw new Error('No active workflow for user')
    }

    context.conversationHistory.push({
      role,
      content,
      timestamp: new Date()
    })

    // Save to database after each message
    await this.saveWorkflowToDB(userId, context)
  }

  public getContext(userId: string): WorkflowContext | undefined {
    return this.activeContexts.get(userId)
  }

  public async completeWorkflow(userId: string, gatheredInformation: Record<string, unknown>): Promise<void> {
    const context = this.activeContexts.get(userId)
    if (!context) {
      throw new Error('No active workflow for user')
    }

    context.isComplete = true
    context.completedAt = new Date()
    context.gatheredInformation = gatheredInformation

    // Save to database
    await this.saveWorkflowToDB(userId, context)

    console.log(`✅ Completed ${context.workflowId} workflow for user ${userId}`)
  }

  public async hasCompletedWorkflow(userId: string, workflowId: string): Promise<boolean> {
    try {
      const supabase = await createServiceRoleClient()

      const { data } = await supabase
        .from('conversations')
        .select('id')
        .eq('user_id', userId)
        .eq('workflow_id', workflowId)
        .eq('is_complete', true)
        .limit(1)
        .single()

      return !!data
    } catch {
      return false
    }
  }

  public getWorkflowConfig(workflowId: string): WorkflowConfig | undefined {
    return this.workflowConfigs.get(workflowId)
  }

  // Database persistence methods
  private async loadWorkflowFromDB(userId: string, workflowId: string): Promise<WorkflowContext | null> {
    try {
      const supabase = await createServiceRoleClient()

      // Get the most recent incomplete conversation for this workflow
      const { data: conversation } = await supabase
        .from('conversations')
        .select('*')
        .eq('user_id', userId)
        .eq('workflow_id', workflowId)
        .eq('is_complete', false)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (!conversation) return null

      const definition = this.getWorkflow(workflowId)
      if (!definition) return null

      return {
        workflowId,
        definition,
        conversationHistory: conversation.conversation_history || [],
        gatheredInformation: conversation.gathered_information || {},
        isComplete: conversation.is_complete || false,
        startedAt: new Date(conversation.started_at),
        completedAt: conversation.completed_at ? new Date(conversation.completed_at) : undefined
      }
    } catch (error) {
      console.error('Error loading workflow from DB:', error)
      return null
    }
  }

  private async saveWorkflowToDB(userId: string, context: WorkflowContext): Promise<void> {
    try {
      const supabase = await createServiceRoleClient()

      // Check if conversation already exists
      const { data: existing } = await supabase
        .from('conversations')
        .select('id')
        .eq('user_id', userId)
        .eq('workflow_id', context.workflowId)
        .eq('is_complete', false)
        .single()

      const conversationData = {
        user_id: userId,
        workflow_id: context.workflowId,
        conversation_history: context.conversationHistory,
        gathered_information: context.gatheredInformation,
        is_complete: context.isComplete,
        completed_at: context.completedAt?.toISOString() || null
      }

      if (existing) {
        // Update existing conversation
        await supabase
          .from('conversations')
          .update(conversationData)
          .eq('id', existing.id)
      } else {
        // Create new conversation
        await supabase
          .from('conversations')
          .insert(conversationData)
      }
    } catch (error) {
      console.error('Error saving workflow to DB:', error)
    }
  }

  public generateContextPrompt(userId: string): string {
    const context = this.activeContexts.get(userId)
    if (!context) {
      throw new Error('No active workflow for user')
    }

    const { definition, conversationHistory } = context

    const prompt = `You are an AI assistant helping with the "${definition.id}" workflow.

WORKFLOW GOAL:
${definition.goal}

CONVERSATION STYLE:
${definition.conversationStyle}

CONVERSATION GUIDELINES:
${definition.conversationGuidelines.map((guideline, i) => `${i + 1}. ${guideline}`).join('\n')}

REQUIRED INFORMATION TO GATHER:
${definition.requiredInformation.map(info => `- ${info}`).join('\n')}

COMPLETION CRITERIA:
${definition.completionCriteria.map((criteria, i) => `${i + 1}. ${criteria}`).join('\n')}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚨 CRITICAL INSTRUCTION - WORKFLOW COMPLETION 🚨
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

BEFORE responding to the user, FIRST evaluate if ALL ${definition.completionCriteria.length} completion criteria are met:

✅ If ALL criteria are satisfied:
   → Call the 'completeWorkflow' tool IMMEDIATELY
   → Do NOT send a text response
   → Do NOT ask more questions

❌ If ANY criteria are missing:
   → Continue the conversation
   → Ask questions to gather the missing information

EVALUATION CHECKLIST (check each one):
${definition.completionCriteria.map((criteria, i) => `[ ] ${i + 1}. ${criteria}`).join('\n')}

If all boxes can be checked ✅, call 'completeWorkflow' NOW.

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

SAMPLE OPENING (if this is the start of conversation):
${definition.sampleOpening}

SAMPLE FOLLOW-UP QUESTIONS (use as inspiration):
${definition.sampleFollowupQuestions.map(q => `- ${q}`).join('\n')}

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n')}

Remember: Follow the conversation style and guidelines. Ask natural, open-ended questions. Listen actively and follow interesting threads. Don't rush through a checklist - let the conversation develop organically while working toward the completion criteria.

⚠️  IMPORTANT: As soon as ALL ${definition.completionCriteria.length} completion criteria are met, call the 'completeWorkflow' tool. Do not ask additional questions after the criteria are satisfied.`

    return prompt
  }
}

export const workflowEngine = new WorkflowEngine()
