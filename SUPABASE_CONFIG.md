# Supabase Configuration for Local Development

## Required Settings in Supabase Dashboard

Go to your Supabase project dashboard: https://supabase.com/dashboard/project/skirnmevbjkstpattfpw

### 1. Authentication > URL Configuration

**Site URL:** (Primary URL used in email templates)
```
https://ai-couple-mediation-assistant.vercel.app
```

**Redirect URLs:** (Whitelist of allowed redirect destinations)
Add these URLs to the allowed redirect URLs list:
```
http://localhost:3001/auth/confirm
http://localhost:3001/auth/callback
http://localhost:3001/**
https://ai-couple-mediation-assistant.vercel.app/auth/confirm
https://ai-couple-mediation-assistant.vercel.app/auth/callback
https://ai-couple-mediation-assistant.vercel.app/**
```

### 2. For Production Deployment

**Site URL:**
```
https://ai-couple-mediation-assistant.vercel.app
```

**Redirect URLs:**
```
https://ai-couple-mediation-assistant.vercel.app/auth/confirm
https://ai-couple-mediation-assistant.vercel.app/auth/callback
https://ai-couple-mediation-assistant.vercel.app/**
```

## Current Issue

The email confirmation is failing because:
1. The redirect URL `http://localhost:3001/auth/confirm` is not in the allowed redirect URLs list
2. Need to add both local and production URLs to support both environments

## After Making These Changes

1. Save the configuration in Supabase dashboard
2. Test the signup flow again
3. The email confirmation should work properly

## Email Confirmation Flow

1. User signs up → receives email
2. Clicks "Confirm your mail" link
3. Redirects to `/auth/confirm` with token
4. Server exchanges token for session
5. Redirects to `/onboarding`
