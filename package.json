{"private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint", "test:workflow": "npx tsx scripts/test-workflow-completion.ts", "test:coaching": "npx tsx scripts/test-coaching-workflow.ts", "test:onboarding": "npx tsx scripts/test-onboarding-completion.ts"}, "dependencies": {"@ai-sdk/openai": "^2.0.38", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.2", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "ai": "^5.0.56", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "latest", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "dotenv": "^17.2.3", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}