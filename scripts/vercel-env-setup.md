# Vercel Environment Variables Setup

## Issue
The remote deployment is failing with a 500 error because Vercel doesn't have the required Supabase environment variables.

## Required Environment Variables for Vercel

Add these environment variables in your Vercel dashboard:

1. Go to: https://vercel.com/dashboard
2. Select your project: `ai-couple-mediation-assistant`
3. Go to **Settings** → **Environment Variables**
4. Add the following variables:

### Environment Variables to Add:

```
NEXT_PUBLIC_SUPABASE_URL=https://skirnmevbjkstpattfpw.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNraXJubWV2Ymprc3RwYXR0ZnB3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTkwNTcyNjUsImV4cCI6MjA3NDYzMzI2NX0.iKbKz5XKf26mc50JeLw1VdFucBUCJp4XdxTKR3JfT0M
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNraXJubWV2Ymprc3RwYXR0ZnB3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTkwNTcyNjUsImV4cCI6MjA3NDYzMzI2NX0.iKbKz5XKf26mc50JeLw1VdFucBUCJp4XdxTKR3JfT0M
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNraXJubWV2Ymprc3RwYXR0ZnB3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTkwNTcyNjUsImV4cCI6MjA3NDYzMzI2NX0.iKbKz5XKf26mc50JeLw1VdFucBUCJp4XdxTKR3JfT0M
```

### Environment Settings:
- **Environment**: Select `Production`, `Preview`, and `Development` for all variables
- **Branch**: Leave blank (applies to all branches)

## After Adding Environment Variables:

1. **Redeploy**: Vercel will automatically redeploy after adding environment variables
2. **Wait**: Give it 2-3 minutes for the deployment to complete
3. **Test**: Visit https://ai-couple-mediation-assistant.vercel.app/onboarding

## Expected Behavior After Fix:

- `/onboarding` should redirect to `/auth/login` (user not logged in)
- `/auth/login` should load the login form
- `/auth/sign-up` should load the signup form
- After signup → should redirect to `/onboarding`
- After completing onboarding → should redirect to `/dashboard`

## Testing the Complete Flow:

1. Visit: https://ai-couple-mediation-assistant.vercel.app/auth/sign-up
2. Create a new account
3. Check email for confirmation (if email confirmation is enabled)
4. Should redirect to `/onboarding`
5. Complete the 4-step onboarding process
6. Should redirect to `/dashboard`

## Current Status:
- ✅ Local development working
- ✅ Database migration completed
- ✅ Authentication flow working locally
- 🔄 Remote deployment needs environment variables
- ⏳ End-to-end testing pending environment variable setup
