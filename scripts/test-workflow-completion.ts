/**
 * Automated test script for workflow completion
 *
 * Run with: npm run test:workflow
 *
 * This script simulates a conversation with the AI and verifies that:
 * 1. The AI calls the completeWorkflow tool when criteria are met
 * 2. The tool receives the correct parameters
 * 3. The workflow completes successfully
 */

// Load environment variables from .env.local
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import { streamText, generateObject } from 'ai'
import { z } from 'zod'
import { workflowEngine } from '../lib/workflows/workflow-engine'

async function testWorkflowCompletion() {
  console.log('🧪 Testing Workflow Completion...\n')

  const TEST_USER_ID = 'test-user-' + Date.now()
  const TEST_WORKFLOW_ID = 'onboarding'

  try {
    // 1. Initialize workflow
    console.log('1️⃣  Initializing workflow...')
    await workflowEngine.startWorkflow(TEST_WORKFLOW_ID, TEST_USER_ID)
    console.log('✅ Workflow initialized\n')

    // 2. Simulate a complete conversation that explicitly covers ALL criteria
    console.log('2️⃣  Simulating conversation...')
    const conversationMessages = [
      { role: 'user' as const, content: 'Hi, my name is Martin' },
      { role: 'assistant' as const, content: 'Nice to meet you Martin! Tell me about your relationship situation and current situation.' },
      { role: 'user' as const, content: 'My partner Sarah and I have been together for 3 years. We live together in an apartment. Currently we\'re having communication issues that are causing stress in our relationship.' },
      { role: 'assistant' as const, content: 'I understand. What are your main communication challenges and pain points?' },
      { role: 'user' as const, content: 'The main pain points are: she calls me constantly throughout the day when I\'m working, I feel pressured and overwhelmed, and we end up arguing about it. I need more space during work hours.' },
      { role: 'assistant' as const, content: 'That sounds challenging. What are your goals for improving communication?' },
      { role: 'user' as const, content: 'My goals are: establish agreed check-in times instead of constant calls, create boundaries around work time, improve our overall communication patterns, and reduce the arguments we have about this.' },
      { role: 'assistant' as const, content: 'Those are great goals. What are your emotional needs and what makes you feel heard?' },
      { role: 'user' as const, content: 'I need to feel trusted that I care about her even when I\'m not available. I feel heard when she acknowledges my need for focus time during work. I need appreciation for my efforts to communicate better.' },
      { role: 'assistant' as const, content: 'That makes sense. What\'s your communication style and preferences?' },
      { role: 'user' as const, content: 'I prefer structured communication at set times rather than random interruptions. I like face-to-face evening conversations - those work really well for us. I need clear boundaries and predictable communication patterns.' },
      { role: 'assistant' as const, content: 'And will your partner be joining this process?' },
      { role: 'user' as const, content: 'Yes, my partner Sarah will definitely be joining this process. She\'s committed to working on our communication together.' },
    ]

    // Add messages to workflow engine
    for (const msg of conversationMessages) {
      await workflowEngine.addMessage(TEST_USER_ID, msg.role, msg.content)
    }
    console.log(`✅ Added ${conversationMessages.length} messages to conversation\n`)
    console.log('📝 Conversation explicitly covers ALL criteria:')
    console.log('   ✅ 1. Relationship context and current situation (3 years, living together, communication issues)')
    console.log('   ✅ 2. Main communication challenges and pain points (constant calls, pressure, arguments)')
    console.log('   ✅ 3. Goals for improving communication (check-in times, boundaries, better patterns)')
    console.log('   ✅ 4. Emotional needs and what makes them feel heard (trust, acknowledgment, appreciation)')
    console.log('   ✅ 5. Communication style and preferences (structured, set times, face-to-face)')
    console.log('   ✅ 6. Partner joining the process (yes, Sarah will join)\n')

    // 3. Generate context prompt
    console.log('3️⃣  Generating context prompt...')
    const contextPrompt = workflowEngine.generateContextPrompt(TEST_USER_ID)
    console.log('✅ Context prompt generated\n')

    // 4. Get workflow context for evaluation
    const context = workflowEngine.getContext(TEST_USER_ID)
    if (!context) {
      throw new Error('No workflow context found')
    }

    const { definition } = context

    // 5. Call AI with the conversation
    console.log('4️⃣  Generating AI response...')
    const result = streamText({
      model: 'openai/gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: contextPrompt
        },
        ...conversationMessages
      ],
      temperature: 0.7,
      maxRetries: 0,
    })

    // 6. Consume the stream
    console.log('5️⃣  Streaming AI response...\n')
    let fullResponse = ''
    for await (const chunk of result.textStream) {
      fullResponse += chunk
      process.stdout.write(chunk)
    }
    console.log('\n')

    // 7. Add AI response to conversation history
    workflowEngine.addMessage(TEST_USER_ID, 'assistant', fullResponse)

    // 8. Evaluate if workflow should be completed
    console.log('6️⃣  Evaluating workflow completion...')

    const updatedContext = workflowEngine.getContext(TEST_USER_ID)!
    const evaluationPrompt = `You are evaluating if a conversation has gathered SUFFICIENT information to complete the "${TEST_WORKFLOW_ID}" workflow.

COMPLETION CRITERIA (mark as met if there is reasonable information, not perfect detail):
${definition.completionCriteria.map((criteria, i) => `${i + 1}. ${criteria}`).join('\n')}

CONVERSATION HISTORY:
${updatedContext.conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n')}

EVALUATION GUIDELINES:
- Look for SUFFICIENT information, not perfect or exhaustive detail
- If the user has provided basic information about each criteria, consider it met
- Don't require deep psychological analysis - basic understanding is enough
- Focus on whether you have enough to help them move forward

If you have sufficient information for ALL criteria:
- Set "complete" to true
- Provide a comprehensive summary of the conversation
- Extract key insights as a JSON object with relevant fields (e.g., name, goals, challenges, etc.)

If you need more information for ANY criteria:
- Set "complete" to false
- Leave summary and keyInsights empty`

    const evaluation = await generateObject({
      model: 'openai/gpt-4o-mini',
      schema: z.object({
        complete: z.boolean().describe('Whether all completion criteria are met'),
        summary: z.string().optional().describe('Summary of the conversation if complete'),
        keyInsights: z.record(z.string(), z.any()).optional().describe('Key insights extracted from the conversation'),
        missingCriteria: z.array(z.string()).optional().describe('List of criteria that are not yet met'),
      }),
      prompt: evaluationPrompt,
      maxRetries: 0,
    })

    console.log('✅ Evaluation complete\n')

    if (evaluation.object.missingCriteria && evaluation.object.missingCriteria.length > 0) {
      console.log('⚠️  Missing criteria:')
      evaluation.object.missingCriteria.forEach((criteria, i) => {
        console.log(`   ${i + 1}. ${criteria}`)
      })
      console.log()
    }

    // 9. Verify results
    console.log('7️⃣  Verifying results...\n')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('📊 TEST RESULTS')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log(`Workflow complete: ${evaluation.object.complete ? '✅ YES' : '❌ NO'}`)
    console.log(`Has summary: ${evaluation.object.summary ? '✅ YES' : '❌ NO'}`)
    console.log(`Has key insights: ${evaluation.object.keyInsights ? '✅ YES' : '❌ NO'}`)

    if (evaluation.object.complete) {
      console.log('\n✅ SUCCESS: Workflow completion criteria met!')
      console.log('\n📋 Captured Data:')
      console.log('  - Summary:', evaluation.object.summary ? '✅' : '❌')
      console.log('  - Key Insights:', evaluation.object.keyInsights ? '✅' : '❌')

      if (evaluation.object.summary) {
        console.log('\n📝 Summary:')
        console.log(evaluation.object.summary)
      }

      if (evaluation.object.keyInsights) {
        console.log('\n🔍 Key Insights Details:')
        for (const [key, value] of Object.entries(evaluation.object.keyInsights)) {
          console.log(`  - ${key}:`, value)
        }
      }

      console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      console.log('✅ TEST PASSED')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n')
      process.exit(0)
    } else {
      console.log('\n❌ FAILURE: Workflow completion criteria NOT met')
      console.log('\nPossible reasons:')
      console.log('  1. AI evaluation determined not all criteria are satisfied')
      console.log('  2. Conversation needs more information')
      console.log('  3. Evaluation prompt needs adjustment')
      console.log('\n💡 AI Response:', fullResponse)
      console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      console.log('❌ TEST FAILED')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n')
      process.exit(1)
    }

  } catch (error) {
    console.error('\n❌ ERROR:', error)
    console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('❌ TEST FAILED WITH ERROR')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n')
    process.exit(1)
  }
}

// Run the test
testWorkflowCompletion()

