import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

async function testOnboardingCompletion() {
  console.log('🧪 Testing Onboarding Completion Flow...\n')

  const supabase = createClient(supabaseUrl, supabaseKey)

  try {
    // 1. Check existing partner profiles
    console.log('1️⃣  Checking existing partner profiles...')
    const { data: profiles, error: profilesError } = await supabase
      .from('partner_profiles')
      .select('id, first_name, onboarding_completed')
      .limit(5)

    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError)
      return
    }

    console.log(`✅ Found ${profiles?.length || 0} partner profiles:`)
    profiles?.forEach(profile => {
      console.log(`   - ${profile.first_name} (${profile.id}): onboarding_completed = ${profile.onboarding_completed}`)
    })
    console.log('')

    // 2. Check conversations
    console.log('2️⃣  Checking onboarding conversations...')
    const { data: conversations, error: conversationsError } = await supabase
      .from('conversations')
      .select('user_id, workflow_id, is_complete, completed_at')
      .eq('workflow_id', 'onboarding')
      .limit(5)

    if (conversationsError) {
      console.error('❌ Error fetching conversations:', conversationsError)
      return
    }

    console.log(`✅ Found ${conversations?.length || 0} onboarding conversations:`)
    conversations?.forEach(conv => {
      console.log(`   - User ${conv.user_id}: is_complete = ${conv.is_complete}, completed_at = ${conv.completed_at}`)
    })
    console.log('')

    // 3. Test the update logic
    if (profiles && profiles.length > 0) {
      const testProfile = profiles[0]
      console.log(`3️⃣  Testing onboarding completion update for user ${testProfile.id}...`)
      
      // Simulate the onboarding completion update
      const { error: updateError } = await supabase
        .from('partner_profiles')
        .update({ onboarding_completed: true })
        .eq('id', testProfile.id)

      if (updateError) {
        console.error('❌ Error updating profile:', updateError)
        return
      }

      // Verify the update
      const { data: updatedProfile, error: fetchError } = await supabase
        .from('partner_profiles')
        .select('onboarding_completed')
        .eq('id', testProfile.id)
        .single()

      if (fetchError) {
        console.error('❌ Error fetching updated profile:', fetchError)
        return
      }

      console.log(`✅ Profile update successful: onboarding_completed = ${updatedProfile.onboarding_completed}`)
      console.log('')
    }

    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('📊 TEST RESULTS')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('Database connection: ✅ WORKING')
    console.log('Partner profiles table: ✅ ACCESSIBLE')
    console.log('Conversations table: ✅ ACCESSIBLE')
    console.log('Profile update logic: ✅ WORKING')
    console.log('')
    console.log('🔍 Potential Issues to Check:')
    console.log('  1. Are users completing onboarding but onboarding_completed staying false?')
    console.log('  2. Is the API route actually calling the update logic?')
    console.log('  3. Are there any RLS (Row Level Security) issues?')
    console.log('')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('✅ ONBOARDING COMPLETION TEST PASSED')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  }
}

testOnboardingCompletion()
