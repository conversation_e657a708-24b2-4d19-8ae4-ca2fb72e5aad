# Database Migration Instructions

## Step 1: Run the Partner Profiles Migration

1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/skirnmevbjkstpattfpw
2. Navigate to **SQL Editor** in the left sidebar
3. Click **New Query**
4. Copy and paste the contents of `supabase/migrations/001_partner_profiles.sql`
5. Click **Run** to execute the migration

## Step 2: Verify the Migration

After running the migration, you should see:
- `partner_profiles` table created
- Indexes created for performance
- Row Level Security enabled
- Policies created for data access

## Step 3: Test the Application

1. Start the local development server: `npm run dev`
2. Go to http://localhost:3000
3. Sign up for a new account
4. You should be redirected to `/onboarding`
5. Complete the 4-step onboarding process
6. Verify you're redirected to `/dashboard`

## Migration SQL File Location

The migration file is located at: `supabase/migrations/001_partner_profiles.sql`

## What This Migration Creates

- **partner_profiles table**: Stores user profile data and onboarding information
- **Indexes**: For performance on common queries
- **RLS Policies**: Security policies to protect user data
- **Triggers**: Automatic timestamp updates
- **Test data**: Sample profile for development (optional)

## Next Steps

Once this migration is complete, we'll have:
✅ Database schema for partner profiles
✅ Complete onboarding flow (4 steps)
✅ Dashboard with profile summary
✅ Server Actions for data management
✅ Authentication integration

This completes the **Partner Onboarding Feature** end-to-end!
